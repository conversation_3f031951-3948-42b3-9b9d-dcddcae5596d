"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFeedbackModule = void 0;
const common_1 = require("@nestjs/common");
const user_feedback_service_1 = require("./user-feedback.service");
const user_feedback_controller_1 = require("./user-feedback.controller");
const typeorm_1 = require("@nestjs/typeorm");
const user_feedback_entity_1 = require("./entities/user-feedback.entity");
let UserFeedbackModule = class UserFeedbackModule {
};
exports.UserFeedbackModule = UserFeedbackModule;
exports.UserFeedbackModule = UserFeedbackModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([user_feedback_entity_1.UserFeedback])],
        controllers: [user_feedback_controller_1.UserFeedbackController],
        providers: [user_feedback_service_1.UserFeedbackService],
    })
], UserFeedbackModule);
//# sourceMappingURL=user-feedback.module.js.map