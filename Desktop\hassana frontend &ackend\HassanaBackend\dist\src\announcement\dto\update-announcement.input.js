"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAnnouncementInput = void 0;
const create_announcement_input_1 = require("./create-announcement.input");
const graphql_1 = require("@nestjs/graphql");
const swagger_1 = require("@nestjs/swagger");
let UpdateAnnouncementInput = class UpdateAnnouncementInput extends (0, graphql_1.PartialType)(create_announcement_input_1.CreateAnnouncementInput) {
};
exports.UpdateAnnouncementInput = UpdateAnnouncementInput;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", String)
], UpdateAnnouncementInput.prototype, "id", void 0);
exports.UpdateAnnouncementInput = UpdateAnnouncementInput = __decorate([
    (0, graphql_1.InputType)()
], UpdateAnnouncementInput);
//# sourceMappingURL=update-announcement.input.js.map