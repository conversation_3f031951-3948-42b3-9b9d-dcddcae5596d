import { EventService } from './event.service';
import { CreateEventInput } from './dto/create-event.input';
import { UpdateEventInput } from './dto/update-event.input';
import { UUID } from 'crypto';
export declare class EventResolver {
    private readonly eventService;
    constructor(eventService: EventService);
    createEvent(createEventInput: CreateEventInput): Promise<import("./entities/event.entity").Event>;
    findAll(): Promise<import("./entities/event.entity").Event[]>;
    findOne(id: UUID): Promise<import("./entities/event.entity").Event>;
    TodaysEvents(date: Date, category: string): Promise<import("./entities/event.entity").Event[]>;
    updateEvent(id: UUID, updateEventInput: UpdateEventInput): Promise<import("./entities/event.entity").Event>;
    removeEvent(id: UUID): Promise<import("./entities/event.entity").Event>;
}
