{"version": 3, "file": "Emailer.js", "sourceRoot": "", "sources": ["../../src/Emailer.ts"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AACzC,MAAM,aAAa,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAE3D,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAC7B,UAAU,CAAC,eAAe,CACxB,aAAa,CAAC;IACZ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;IAC3B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;IAC3B,MAAM,EAAE,KAAK;IACb,IAAI,EAAE;QACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;QAC3B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa;KAChC;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,KAAK;KAC1B;CACF,CAAC,CACH,CAAC;AAEG,MAAM,QAAQ,GAAG,CAAC,EAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAC,EAAE,EAAE;IAC1D,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;IAExC,WAAW,CAAC,QAAQ,CAAC,EAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;QACpE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,WAAW,CAAC,KAAK,EAAE,CAAC;AACtB,CAAC,CAAC;AAXW,QAAA,QAAQ,YAWnB;AAEK,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,EAAE;IAE3C,MAAM,SAAS,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;IAEzE,IAAA,gBAAQ,EAAC;QACP,IAAI,EAAE,mCAAmC;QAEzC,EAAE,EAAE,8BAA8B;QAGlC,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE,IAAI;QACV,IAAI,EAAE;;kGAEwF,IAAI,IAAI,CAC5F,OAAO,CAAC,KAAK,CACd,CAAC,YAAY,EAAE;;;;iBAIX,SAAS;iBACT,OAAO,CAAC,QAAQ;;aAEpB;KACV,CAAC,CAAC;AAEL,CAAC,CAAC;AA1BW,QAAA,iBAAiB,qBA0B5B;AAEK,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,EAAE;IAChD,IAAA,gBAAQ,EAAC;QACP,IAAI,EAAE,4BAA4B;QAGlC,EAAE,EAAE,8BAA8B;QAClC,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE,iHACJ,OAAO,CAAC,QACV,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,OAAO,IAAI,IAAI,CAC1D,OAAO,CAAC,GAAG,CACZ,CAAC,kBAAkB,EAAE,4MACpB,OAAO,CAAC,QACV,EAAE;QACF,IAAI,EAAE,IAAI;KACX,CAAC,CAAC;AACL,CAAC,CAAC;AAhBW,QAAA,sBAAsB,0BAgBjC"}