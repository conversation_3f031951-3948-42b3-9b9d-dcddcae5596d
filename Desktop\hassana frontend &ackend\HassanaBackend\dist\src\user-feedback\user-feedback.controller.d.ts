import { UserFeedbackService } from './user-feedback.service';
import { CreateUserFeedbackDto } from './dto/create-user-feedback.dto';
import { UUID } from 'crypto';
export declare class UserFeedbackController {
    private readonly userFeedbackService;
    constructor(userFeedbackService: UserFeedbackService);
    create(createUserFeedbackDto: CreateUserFeedbackDto, req: Request): Promise<{
        status: boolean;
        message: string;
        data: import("./entities/user-feedback.entity").UserFeedback;
        errorMessage?: undefined;
    } | {
        status: boolean;
        message: string;
        errorMessage: any;
        data?: undefined;
    }>;
    findAll(): Promise<{
        status: boolean;
        message: string;
        data: import("./entities/user-feedback.entity").UserFeedback[];
        errorMessage?: undefined;
    } | {
        status: boolean;
        message: string;
        errorMessage: any;
        data?: undefined;
    }>;
    remove(id: UUID): Promise<{
        status: boolean;
        message: string;
        data: import("./entities/user-feedback.entity").UserFeedback;
        errorMessage?: undefined;
    } | {
        status: boolean;
        message: string;
        errorMessage: any;
        data?: undefined;
    }>;
}
