import { NotificationService } from './notification.service';
import { CreateNotificationInput } from './dto/create-notification.input';
import { UpdateNotificationInput } from './dto/update-notification.input';
import { NotificationViewEntity } from './entities/notification.entity';
import { UUID } from 'crypto';
export declare class NotificationResolver {
    private readonly notificationService;
    constructor(notificationService: NotificationService);
    createNotification(createNotificationInput: CreateNotificationInput): Promise<CreateNotificationInput & import("./entities/notification.entity").NotificationEntity>;
    addNotificationView(notificationId: UUID, userId: UUID): Promise<NotificationViewEntity>;
    notifications(): Promise<import("./entities/notification.entity").NotificationEntity[]>;
    notificationViews(): Promise<NotificationViewEntity[]>;
    newNotificationsForUser(id: number): Promise<import("./entities/notification.entity").NotificationEntity[]>;
    notification(id: UUID): string;
    updateNotification(id: UUID, updateNotificationInput: UpdateNotificationInput): Promise<UpdateNotificationInput & import("./entities/notification.entity").NotificationEntity>;
    removeNotification(id: UUID): Promise<import("./entities/notification.entity").NotificationEntity>;
}
