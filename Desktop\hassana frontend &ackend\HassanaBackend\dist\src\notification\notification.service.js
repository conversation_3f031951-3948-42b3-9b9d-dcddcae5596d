"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const notification_entity_1 = require("./entities/notification.entity");
const typeorm_2 = require("@nestjs/typeorm");
const user_entity_1 = require("../users/entities/user.entity");
let NotificationService = class NotificationService {
    constructor(notificationRepo, notificationViewRepo, userRepo) {
        this.notificationRepo = notificationRepo;
        this.notificationViewRepo = notificationViewRepo;
        this.userRepo = userRepo;
    }
    async create(createNotificationInput) {
        try {
            const response = await this.notificationRepo.save(createNotificationInput);
            return response;
        }
        catch (error) {
            console.log(error);
            throw Error(error);
        }
    }
    async addView(notificationId, userId) {
        try {
            const existingView = await this.notificationViewRepo.findOne({
                where: {
                    user: { id: userId },
                    notification: { id: notificationId },
                },
            });
            if (existingView) {
                console.log('User has already viewed this notification.');
                return existingView;
            }
            const newView = new notification_entity_1.NotificationViewEntity();
            newView.user = { id: userId };
            newView.notification = { id: notificationId };
            const savedView = await this.notificationViewRepo.save(newView);
            console.log('New view added:', savedView);
            return savedView;
        }
        catch (error) {
            console.error('Error in setUserViewedNotification:', error.message);
            throw new Error(error);
        }
    }
    async findAllNotificationViews() {
        try {
            let response = await this.notificationViewRepo.find();
            console.log(response);
            return response;
        }
        catch (error) {
            console.log(error.message);
            throw Error(error);
        }
    }
    async findAll() {
        try {
            let response = await this.notificationRepo.find();
            return response;
        }
        catch (error) {
            console.log(error.message);
            throw Error(error);
        }
    }
    async getAllNewNotification() {
        try {
            let response = await this.notificationRepo.find({ relations: ['NotificationViewEntity'] });
            return response;
        }
        catch (error) {
            console.log(error.message);
            throw Error(error);
        }
    }
    async getAllNewNotificationsForUser(userId) {
        try {
            const viewedNotificationsSubquery = this.notificationViewRepo.createQueryBuilder('view')
                .select('view.notificationId')
                .where('view.userId = :userId', { userId });
            const queryBuilder = this.notificationRepo.createQueryBuilder('notification');
            queryBuilder.where(`notification.id NOT IN (${viewedNotificationsSubquery.getQuery()})`)
                .setParameters({ userId });
            const response = await queryBuilder.getMany();
            console.log(response);
            return response;
        }
        catch (error) {
            console.error('Error in getAllNewNotificationsForUser:', error.message);
            throw new Error(error);
        }
    }
    findOne(id) {
        return `This action returns a #${id} notification`;
    }
    async update(id, updateNotificationInput) {
        try {
            let oldData = await this.notificationRepo.findOne({ where: { id: id } });
            console.log(oldData);
            if (!oldData)
                throw new common_1.NotFoundException("Quote not found");
            if (oldData) {
                this.notificationRepo.merge(oldData, updateNotificationInput);
                let newData = await this.notificationRepo.save(updateNotificationInput);
                return newData;
            }
        }
        catch (error) {
            throw new Error(error.message);
        }
    }
    async remove(id) {
        try {
            let data = await this.notificationRepo.findOne({ where: { id: id } });
            if (!data)
                throw new common_1.NotFoundException("Notification not found");
            if (data) {
                return await this.notificationRepo.remove(data);
            }
        }
        catch (error) {
            throw new Error(error.message);
        }
    }
};
exports.NotificationService = NotificationService;
exports.NotificationService = NotificationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(notification_entity_1.NotificationEntity)),
    __param(1, (0, typeorm_2.InjectRepository)(notification_entity_1.NotificationViewEntity)),
    __param(2, (0, typeorm_2.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository])
], NotificationService);
//# sourceMappingURL=notification.service.js.map