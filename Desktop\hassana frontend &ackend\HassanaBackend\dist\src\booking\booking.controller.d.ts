import { BookingService } from './booking.service';
import { CreateBookingInput } from './dto/create-booking.input';
export declare class BookingController {
    private readonly bookingService;
    constructor(bookingService: BookingService);
    createBooking(file: Express.Multer.File, createBookingInput: CreateBookingInput): Promise<{
        code: number;
        message: string;
        data: import("./entities/booking.entity").Booking;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
}
