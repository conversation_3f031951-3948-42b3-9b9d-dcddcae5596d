"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventService = void 0;
const common_1 = require("@nestjs/common");
const event_entity_1 = require("./entities/event.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let EventService = class EventService {
    constructor(eventRepository) {
        this.eventRepository = eventRepository;
    }
    async create(createEventInput) {
        const newEvent = this.eventRepository.create({
            ...createEventInput
        });
        const savedEvent = await this.eventRepository.save(newEvent);
        return { ...savedEvent, id: savedEvent.id };
    }
    findAll() {
        return this.eventRepository.find();
    }
    findOne(id) {
        return this.eventRepository.findOne({ where: { id } });
    }
    findTodaysEvent(date, category) {
        const startDate = new Date(date);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(date);
        endDate.setHours(23, 59, 59, 999);
        return this.eventRepository.find({ where: { date: (0, typeorm_2.Between)(startDate, endDate), category: category, status: true } });
    }
    async update(id, updateEventInput) {
        const existingEvent = await this.eventRepository.findOne({ where: { id } });
        if (existingEvent) {
            this.eventRepository.merge(existingEvent, updateEventInput);
            return this.eventRepository.save(existingEvent);
        }
        return null;
    }
    async remove(id) {
        const eventToRemove = await this.eventRepository.findOne({ where: { id } });
        if (eventToRemove) {
            await this.eventRepository.remove(eventToRemove);
            return eventToRemove;
        }
        return null;
    }
};
exports.EventService = EventService;
exports.EventService = EventService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(event_entity_1.Event)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EventService);
//# sourceMappingURL=event.service.js.map