"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestController = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
const redis_1 = require("../../redis");
let TestController = class TestController {
    async getTesting(lat, lon, res) {
        const redisKey = `weather:${lat}:${lon}`;
        const nearbyLocations = (await redis_1.redis.georadius('weather_locations', lon, lat, 5, 'km'));
        for (let location of nearbyLocations) {
            if (typeof location === 'string') {
                let cacheData = await redis_1.redis.get(location);
                if (cacheData) {
                    console.log('Response from Redis');
                    return res.json({
                        code: 200,
                        message: 'Successfully retrieved from cache',
                        data: JSON.parse(cacheData),
                        source: 'cache',
                    });
                }
            }
        }
        const weatherAPI = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${process.env.WEATHER_API_KEY}`;
        try {
            const response = await axios_1.default.get(weatherAPI);
            await redis_1.redis.geoadd('weather_locations', lon, lat, redisKey);
            await redis_1.redis.set(redisKey, JSON.stringify(response.data), 'EX', 7200);
            console.log('Response from API');
            return res.json({
                code: 200,
                message: 'Successfully retrieved from API',
                data: response.data,
                source: 'API',
            });
        }
        catch (error) {
            return res.json({
                code: 400,
                message: 'Something went wrong',
                error: error.message,
            });
        }
    }
};
exports.TestController = TestController;
__decorate([
    (0, common_1.Get)('testing-route'),
    __param(0, (0, common_1.Query)('lat')),
    __param(1, (0, common_1.Query)('lon')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], TestController.prototype, "getTesting", null);
exports.TestController = TestController = __decorate([
    (0, common_1.Controller)('/v1/test')
], TestController);
//# sourceMappingURL=test.controller.js.map