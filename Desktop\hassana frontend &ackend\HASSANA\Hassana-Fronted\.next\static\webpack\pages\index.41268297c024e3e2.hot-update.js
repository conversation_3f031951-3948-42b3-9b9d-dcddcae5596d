"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject2());\n// Styled components for social media style notifications\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    sx: {\n                                                        p: 2,\n                                                        borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                        \"&:hover\": {\n                                                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        transition: \"all 0.2s ease-in-out\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                gap: 2,\n                                                                alignItems: \"flex-start\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        borderRadius: \"50%\",\n                                                                        //background: th,\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        justifyContent: \"center\",\n                                                                        flexShrink: 0,\n                                                                        boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                                        sx: {\n                                                                            color: \"white\",\n                                                                            fontSize: \"20px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        flex: 1,\n                                                                        minWidth: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"body1\",\n                                                                            sx: {\n                                                                                fontSize: \"14px\",\n                                                                                fontWeight: 500,\n                                                                                lineHeight: \"20px\",\n                                                                                color: \"#333\",\n                                                                                mb: 0.5,\n                                                                                wordBreak: \"break-word\"\n                                                                            },\n                                                                            children: notificationData.notification\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            sx: {\n                                                                                fontSize: \"12px\",\n                                                                                color: \"#666\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\",\n                                                                                gap: 0.5\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    sx: {\n                                                                                        fontSize: \"4px\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                position: \"absolute\",\n                                                                left: 0,\n                                                                top: 0,\n                                                                bottom: 0,\n                                                                width: \"3px\",\n                                                                //background:\n                                                                //  \"linear-gradient(180deg, #667eea 0%, #764ba2 100%)\",\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, notificationData.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 375,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            gap: 1,\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 399,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 456,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showBadgeCount, setShowBadgeCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    // Derive userId from session\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    // Handle session loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 477,\n            columnNumber: 7\n        }, this);\n    }\n    // Query for new notifications\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNewNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    // Notification sound function\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    // Show browser notification\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    // Request notification permission\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    // Debug session object\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    // Handle notification query results\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.newNotificationsForUser);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.newNotificationsForUser)) {\n            const newNotifications = data.newNotificationsForUser;\n            const currentCount = newNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"Notifications received:\", newNotifications);\n            console.log(\"Count:\", currentCount);\n            console.log(\"showBadgeCount state:\", showBadgeCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 3000);\n            }\n            setNotifications(newNotifications);\n            setPreviousNotificationCount(currentCount);\n            // Only show badge count if there are notifications and the panel is not currently open\n            if (currentCount > 0 && !notificationOpen) {\n                setShowBadgeCount(true);\n                console.log(\"Setting showBadgeCount to TRUE because count > 0 and panel is closed\");\n            } else if (currentCount === 0) {\n                setShowBadgeCount(false);\n                console.log(\"Setting showBadgeCount to FALSE because no notifications\");\n            }\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            // Retry polling after 30 seconds\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = ()=>{\n        setNotificationOpen((prev)=>!prev);\n    // Don't automatically mark notifications as viewed when opening the panel\n    // Users need to click on individual notifications to mark them as seen\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        // Keep badge count hidden since notifications were already seen when panel was opened\n        setShowBadgeCount(false);\n    };\n    const markAllNotificationsAsViewed = async ()=>{\n        if (!userId || notifications.length === 0) return;\n        try {\n            const promises = notifications.map((notification)=>addNotificationView({\n                    variables: {\n                        notificationId: notification.id,\n                        user_id: userId\n                    }\n                }));\n            await Promise.all(promises);\n            // Clear notifications and reset counts\n            setNotifications([]);\n            setPreviousNotificationCount(0);\n            setShowBadgeCount(false);\n            console.log(\"All notifications marked as viewed and cleared\");\n        } catch (error) {\n            console.error(\"Error marking all notifications as viewed:\", error);\n        }\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                // Remove the notification from the list immediately\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                // Update badge count to show only unseen notifications\n                setShowBadgeCount(true);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 741,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 748,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 734,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 758,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 751,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: '\"\"',\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 781,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"blue\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"blue\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"green\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"green\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"purple\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"purple\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 827,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 826,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"6px\",\n                                                    sm: \"8px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: notifications.length,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8pxrgb(122, 15, 15))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 896,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 880,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 918,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 768,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 721,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 720,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid #EBEBEB\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 957,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 948,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 970,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 987,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 991,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 985,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 997,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 996,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1007,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1006,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1011,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1005,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1017,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1016,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1021,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1027,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1026,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1025,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1037,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1036,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1041,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1047,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1046,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1051,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 969,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 959,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 940,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 932,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1070,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1065,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Ra7y/5uHrJet5+UvmlxfSvo3ne4=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});