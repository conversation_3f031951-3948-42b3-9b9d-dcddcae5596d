"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const booking_entity_1 = require("./entities/booking.entity");
const path = require("path");
const fs_1 = require("fs");
const Emailer_1 = require("../Emailer");
const user_entity_1 = require("../users/entities/user.entity");
let BookingService = class BookingService {
    constructor(bookingRepository, userRepository) {
        this.bookingRepository = bookingRepository;
        this.userRepository = userRepository;
    }
    async findAll() {
        try {
            const bookingList = await this.bookingRepository.find();
            if (bookingList && bookingList.length > 0) {
                bookingList.forEach(booking => {
                    if (booking.registrationDoc) {
                        booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
                    }
                });
            }
            return bookingList;
        }
        catch (error) {
            console.error('Error: ', error);
            throw error;
        }
    }
    async getAllBookingTeaBoy() {
        try {
            const bookingList = await this.bookingRepository.find({
                where: { teaBoy: "true" }
            });
            if (bookingList && bookingList.length > 0) {
                bookingList.forEach(booking => {
                    if (booking.registrationDoc) {
                        booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
                    }
                });
            }
            return bookingList;
        }
        catch (error) {
            console.error('Error: ', error);
            throw error;
        }
    }
    async findByUser(id) {
        try {
            const bookingList = await this.bookingRepository.find({
                where: { userId: id }
            });
            if (bookingList && bookingList.length > 0) {
                bookingList.forEach(booking => {
                    if (booking.registrationDoc) {
                        booking.registrationDoc = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
                    }
                });
            }
            return bookingList;
        }
        catch (error) {
            console.error('Error: ', error);
            throw error;
        }
    }
    async create(updateBookingInput) {
        try {
            let existingBooking = null;
            existingBooking = await this.bookingRepository.findOne({
                where: { uid: updateBookingInput.uid }
            });
            const user = await this.userRepository.findOne({
                where: { id: updateBookingInput.userId }
            });
            if (existingBooking) {
                if (updateBookingInput.registrationDoc != undefined) {
                    const imageInServer = path.join(__dirname, "../../resource/" + existingBooking.registrationDoc);
                    if ((0, fs_1.existsSync)(imageInServer)) {
                        (0, fs_1.unlinkSync)(imageInServer);
                    }
                }
                this.bookingRepository.merge(existingBooking, updateBookingInput);
                existingBooking.updated_on = new Date();
                let updateData = await this.bookingRepository.save(existingBooking);
                if (updateData) {
                    updateData.registrationDoc = updateData.registrationDoc ? `${process.env.SERVER_URL}/${updateData.registrationDoc}` : null;
                }
                if (existingBooking.parking == "false" && updateBookingInput.parking == 'true') {
                    (0, Emailer_1.sendMailToParking)({ ...updateBookingInput, userName: user.name });
                }
                if (existingBooking.itTechnician == "false" && updateBookingInput.itTechnician == 'true') {
                    (0, Emailer_1.sendMailToItTechnician)({ ...updateBookingInput, userName: user.name });
                }
                return updateData;
            }
            else {
                const res = await this.bookingRepository.save(updateBookingInput);
                if (updateBookingInput.parking == 'true') {
                    (0, Emailer_1.sendMailToParking)({ ...updateBookingInput, userName: user.name });
                }
                if (updateBookingInput.itTechnician == 'true') {
                    (0, Emailer_1.sendMailToItTechnician)({ ...updateBookingInput, userName: user.name });
                }
                res.registrationDoc = res.registrationDoc ? `${process.env.SERVER_URL}/${res.registrationDoc}` : null;
                return res;
            }
        }
        catch (error) {
            throw new Error(`Failed to create or update booking: ${error.message}`);
        }
    }
};
exports.BookingService = BookingService;
exports.BookingService = BookingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(booking_entity_1.Booking)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BookingService);
//# sourceMappingURL=booking.service.js.map