"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnnouncementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const fs_1 = require("fs");
const path = require("path");
const typeorm_2 = require("typeorm");
const announcement_entity_1 = require("./entities/announcement.entity");
let AnnouncementService = class AnnouncementService {
    constructor(announcementRepository, announcementViewRepository) {
        this.announcementRepository = announcementRepository;
        this.announcementViewRepository = announcementViewRepository;
    }
    async create(createAnnouncementInput) {
        const newAnnouncement = this.announcementRepository.create({
            ...createAnnouncementInput
        });
        const savedAnnouncement = await this.announcementRepository.save(newAnnouncement);
        savedAnnouncement.image ? savedAnnouncement.image = `${process.env.SERVER_URL}/${savedAnnouncement.image}` : null;
        return savedAnnouncement;
    }
    async findAll(userId) {
        const announcementList = await this.announcementRepository.createQueryBuilder("announcement")
            .leftJoinAndSelect("announcement_view", "view", "view.announcement_id = announcement.id AND view.user_id = :userId", { userId })
            .select([
            "announcement.id AS id",
            "announcement.title AS title",
            "announcement.details AS details",
            "announcement.category AS category",
            "announcement.status AS status",
            "announcement.visibility AS visibility",
            "announcement.image AS image",
            "CASE WHEN view.id IS NOT NULL THEN true ELSE false END AS is_read"
        ])
            .orderBy("announcement.createdAt", "DESC")
            .getRawMany();
        if (announcementList && announcementList.length > 0) {
            announcementList.forEach(announcement => {
                announcement.image ? announcement.image = `${process.env.SERVER_URL}/${announcement.image}` : null;
            });
        }
        return announcementList;
    }
    ;
    async findOne(id) {
        let announcement = await this.announcementRepository.findOne({ where: { id } });
        if (!announcement)
            throw new common_1.NotFoundException('Announcement not found');
        announcement.image ? announcement.image = `${process.env.SERVER_URL}/${announcement.image}` : null;
        return announcement;
    }
    async update(id, updateAnnouncementInput) {
        const existingAnnouncement = await this.announcementRepository.findOne({ where: { id } });
        if (!existingAnnouncement)
            throw new common_1.NotFoundException('Announcement not found');
        if (updateAnnouncementInput.image) {
            const imageInServer = path?.join(__dirname, "../../resource/" + existingAnnouncement.image);
            if ((0, fs_1.existsSync)(imageInServer)) {
                console.log("deleting Image in server ...: " + imageInServer);
                (0, fs_1.unlinkSync)(imageInServer);
            }
            ;
        }
        ;
        this.announcementRepository.merge(existingAnnouncement, updateAnnouncementInput);
        return this.announcementRepository.save(existingAnnouncement);
    }
    async remove(id) {
        const announcementToRemove = await this.announcementRepository.findOne({ where: { id } });
        if (!announcementToRemove)
            throw new common_1.NotFoundException('Announcement not found');
        if (announcementToRemove) {
            try {
                const imagePath = path?.join(__dirname, "../../resource/" + announcementToRemove.image);
                if ((0, fs_1.existsSync)(imagePath)) {
                    console.log("deleting Image in server ...: " + imagePath);
                    (0, fs_1.unlinkSync)(imagePath);
                }
                ;
                await this.announcementRepository.remove(announcementToRemove);
                return announcementToRemove;
            }
            catch (error) {
                throw error;
            }
        }
        return null;
    }
    async createAnnouncementView(announcementId, userId) {
        await this.announcementViewRepository.upsert({
            announcement_id: announcementId,
            user_id: userId
        }, { conflictPaths: ["announcement_id", "user_id"] });
    }
};
exports.AnnouncementService = AnnouncementService;
exports.AnnouncementService = AnnouncementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(announcement_entity_1.Announcement)),
    __param(1, (0, typeorm_1.InjectRepository)(announcement_entity_1.AnnouncementViewEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AnnouncementService);
//# sourceMappingURL=announcement.service.js.map