"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffersViewEntity = exports.Offers = void 0;
const BaseEntity_1 = require("../../BaseEntity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
let Offers = class Offers extends BaseEntity_1.BaseEntity {
};
exports.Offers = Offers;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Offers.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Offers.prototype, "contact_information", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Offers.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Offers.prototype, "expiry_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Offers.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Boolean)
], Offers.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Offers.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Offers.prototype, "updated_by", void 0);
exports.Offers = Offers = __decorate([
    (0, typeorm_1.Entity)({ name: "offers" })
], Offers);
let OffersViewEntity = class OffersViewEntity extends BaseEntity_1.BaseEntity {
};
exports.OffersViewEntity = OffersViewEntity;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: "user_id" }),
    __metadata("design:type", String)
], OffersViewEntity.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Offers, (offers) => offers.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: "offer_id" }),
    __metadata("design:type", String)
], OffersViewEntity.prototype, "offer_id", void 0);
exports.OffersViewEntity = OffersViewEntity = __decorate([
    (0, typeorm_1.Entity)({ name: "offers_view" }),
    (0, typeorm_1.Unique)(["user_id", "offer_id"])
], OffersViewEntity);
//# sourceMappingURL=offers.entity.js.map