import { NewsService } from './news.service';
export declare class NewsController {
    private readonly newsService;
    constructor(newsService: NewsService);
    createNews(file: Express.Multer.File, createNewsDto: any): Promise<{
        code: number;
        message: string;
        data: any;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    private transformFormData;
    findAllNews(): Promise<{
        code: number;
        message: string;
        data: import("./entities/news.entity").News[];
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    getExternalNews(): Promise<{
        code: number;
        message: string;
        data: import("./entities/news.entity").News[];
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    getInternalNews(): Promise<{
        code: number;
        message: string;
        data: import("./entities/news.entity").News[];
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    updateNews(id: string, updateNewsInput: any, file: Express.Multer.File): Promise<{
        code: number;
        message: string;
        data: import("./entities/news.entity").News;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    removeNews(id: string): Promise<{
        code: number;
        message: string;
        data: import("./entities/news.entity").News;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
}
