"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationViewEntity = exports.NotificationEntity = void 0;
const BaseEntity_1 = require("../../BaseEntity");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
let NotificationEntity = class NotificationEntity extends BaseEntity_1.BaseEntity {
};
exports.NotificationEntity = NotificationEntity;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], NotificationEntity.prototype, "notification", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => NotificationViewEntity, (views) => views.user),
    __metadata("design:type", Array)
], NotificationEntity.prototype, "views", void 0);
exports.NotificationEntity = NotificationEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'notification' })
], NotificationEntity);
let NotificationViewEntity = class NotificationViewEntity extends BaseEntity_1.BaseEntity {
};
exports.NotificationViewEntity = NotificationViewEntity;
__decorate([
    (0, typeorm_1.ManyToOne)(() => NotificationEntity, (notification) => notification.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'notificationId' }),
    __metadata("design:type", NotificationEntity)
], NotificationViewEntity.prototype, "notification", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], NotificationViewEntity.prototype, "user", void 0);
exports.NotificationViewEntity = NotificationViewEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'notification_view' })
], NotificationViewEntity);
//# sourceMappingURL=notification.entity.js.map