"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffersSchema = void 0;
const graphql_1 = require("@nestjs/graphql");
let OffersSchema = class OffersSchema {
};
exports.OffersSchema = OffersSchema;
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OffersSchema.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OffersSchema.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OffersSchema.prototype, "contact_information", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], OffersSchema.prototype, "code", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.GraphQLISODateTime, { nullable: true }),
    __metadata("design:type", Date)
], OffersSchema.prototype, "expiry_date", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OffersSchema.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], OffersSchema.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", Boolean)
], OffersSchema.prototype, "is_read", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.GraphQLISODateTime, { nullable: true }),
    __metadata("design:type", Date)
], OffersSchema.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OffersSchema.prototype, "created_by", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.GraphQLISODateTime, { nullable: true }),
    __metadata("design:type", Date)
], OffersSchema.prototype, "updatedAt", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], OffersSchema.prototype, "updated_by", void 0);
exports.OffersSchema = OffersSchema = __decorate([
    (0, graphql_1.ObjectType)()
], OffersSchema);
//# sourceMappingURL=offers.schema.js.map