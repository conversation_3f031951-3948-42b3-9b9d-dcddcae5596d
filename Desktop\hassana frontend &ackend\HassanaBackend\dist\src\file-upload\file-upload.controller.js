"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystemController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const fs = require("fs");
const path = require("path");
const fs_1 = require("fs");
const mime = require("mime-types");
let FileSystemController = class FileSystemController {
    constructor() {
        this.basePath = path.join(process.cwd(), 'library', 'v1', 'lib');
    }
    getAbsolutePath(userPath = '') {
        const normalizedPath = userPath ? userPath.replace(/\.\.\//g, '') : '';
        const absolutePath = path.resolve(this.basePath, normalizedPath);
        if (!absolutePath.startsWith(this.basePath)) {
            throw new common_1.BadRequestException('Invalid path');
        }
        return absolutePath;
    }
    async createFolder(folderPath, folderName) {
        if (!folderName)
            throw new common_1.BadRequestException('Folder name is required');
        const fullPath = this.getAbsolutePath(folderPath);
        const newFolderPath = path.join(fullPath, folderName);
        if (fs.existsSync(newFolderPath)) {
            throw new common_1.BadRequestException('Folder already exists');
        }
        fs.mkdirSync(newFolderPath, { recursive: true });
        return {
            message: 'Folder created successfully',
            path: newFolderPath.replace(this.basePath, '')
        };
    }
    uploadFile(file, folderPath = '') {
        if (!file)
            throw new common_1.BadRequestException('No file uploaded');
        const targetFolder = this.getAbsolutePath(folderPath);
        if (!fs.existsSync(targetFolder)) {
            fs.mkdirSync(targetFolder, { recursive: true });
        }
        const filePath = path.join(targetFolder, file.originalname);
        fs.writeFileSync(filePath, file.buffer);
        return {
            message: 'File uploaded successfully',
            path: filePath.replace(this.basePath, ''),
            fileName: file.originalname
        };
    }
    listDirectory(folderPath = '') {
        const targetPath = this.getAbsolutePath(folderPath);
        if (!fs.existsSync(targetPath)) {
            throw new common_1.BadRequestException('Path does not exist');
        }
        const items = fs.readdirSync(targetPath);
        const result = [];
        items.forEach(item => {
            const itemPath = path.join(targetPath, item);
            const stat = fs.statSync(itemPath);
            result.push({
                name: item,
                type: stat.isDirectory() ? 'folder' : 'file',
                path: path.join(folderPath, item).replace(/\\/g, '/'),
                size: stat.size,
                createdAt: stat.birthtime,
                updatedAt: stat.mtime
            });
        });
        return result;
    }
    async getFile(filePath, download, res) {
        if (!filePath) {
            throw new common_1.BadRequestException('File path is required');
        }
        const absolutePath = this.getAbsolutePath(filePath);
        if (!fs.existsSync(absolutePath)) {
            throw new common_1.NotFoundException('File not found');
        }
        const stat = fs.statSync(absolutePath);
        if (stat.isDirectory()) {
            throw new common_1.BadRequestException('Cannot download a directory');
        }
        let contentType = mime.lookup(absolutePath) || 'application/octet-stream';
        const ext = path.extname(absolutePath).toLowerCase();
        if (ext === '.png')
            contentType = 'image/png';
        if (ext === '.jpg' || ext === '.jpeg')
            contentType = 'image/jpeg';
        if (ext === '.txt')
            contentType = 'text/plain';
        console.log(`File: ${filePath}, Extension: ${ext}, Content-Type: ${contentType}`);
        const disposition = download === 'true' ? 'attachment' : 'inline';
        res.set({
            'Content-Type': contentType,
            'Content-Disposition': `${disposition}; filename="${encodeURIComponent(path.basename(absolutePath))}"`,
            'Content-Length': stat.size.toString(),
        });
        return new common_1.StreamableFile((0, fs_1.createReadStream)(absolutePath));
    }
    deleteItem(itemPath) {
        if (!itemPath)
            throw new common_1.BadRequestException('Path is required');
        const fullPath = this.getAbsolutePath(itemPath);
        if (!fs.existsSync(fullPath)) {
            throw new common_1.BadRequestException('Path does not exist');
        }
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
            fs.rmSync(fullPath, { recursive: true, force: true });
        }
        else {
            fs.unlinkSync(fullPath);
        }
        return { message: 'Item deleted successfully' };
    }
    renameFile(filePath, newName) {
        if (!filePath || !newName) {
            throw new common_1.BadRequestException('File path and new name are required');
        }
        const absolutePath = this.getAbsolutePath(filePath);
        if (!fs.existsSync(absolutePath)) {
            throw new common_1.NotFoundException('File not found');
        }
        const stat = fs.statSync(absolutePath);
        if (stat.isDirectory()) {
            throw new common_1.BadRequestException('Path points to a directory, use rename-folder endpoint instead');
        }
        if (!newName.match(/^[a-zA-Z0-9._-]+$/)) {
            throw new common_1.BadRequestException('Invalid file name. Use alphanumeric characters, dots, underscores, or hyphens only');
        }
        const directory = path.dirname(absolutePath);
        const newFilePath = path.join(directory, newName);
        if (fs.existsSync(newFilePath)) {
            throw new common_1.BadRequestException('A file with the new name already exists');
        }
        fs.renameSync(absolutePath, newFilePath);
        return {
            message: 'File renamed successfully',
            path: newFilePath.replace(this.basePath, ''),
            fileName: newName
        };
    }
    renameFolder(folderPath, newName) {
        if (!folderPath || !newName) {
            throw new common_1.BadRequestException('Folder path and new name are required');
        }
        const absolutePath = this.getAbsolutePath(folderPath);
        if (!fs.existsSync(absolutePath)) {
            throw new common_1.NotFoundException('Folder not found');
        }
        const stat = fs.statSync(absolutePath);
        if (!stat.isDirectory()) {
            throw new common_1.BadRequestException('Path points to a file, use rename-file endpoint instead');
        }
        if (!newName.match(/^[a-zA-Z0-9._-]+$/)) {
            throw new common_1.BadRequestException('Invalid folder name. Use alphanumeric characters, dots, underscores, or hyphens only');
        }
        const directory = path.dirname(absolutePath);
        const newFolderPath = path.join(directory, newName);
        if (fs.existsSync(newFolderPath)) {
            throw new common_1.BadRequestException('A folder with the new name already exists');
        }
        fs.renameSync(absolutePath, newFolderPath);
        return {
            message: 'Folder renamed successfully',
            path: newFolderPath.replace(this.basePath, ''),
            folderName: newName
        };
    }
};
exports.FileSystemController = FileSystemController;
__decorate([
    (0, common_1.Post)('folder'),
    __param(0, (0, common_1.Body)('path')),
    __param(1, (0, common_1.Body)('folderName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FileSystemController.prototype, "createFolder", null);
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: { fileSize: 50 * 1024 * 1024 },
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], FileSystemController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Get)('list'),
    __param(0, (0, common_1.Query)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FileSystemController.prototype, "listDirectory", null);
__decorate([
    (0, common_1.Get)('file'),
    __param(0, (0, common_1.Query)('path')),
    __param(1, (0, common_1.Query)('download')),
    __param(2, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], FileSystemController.prototype, "getFile", null);
__decorate([
    (0, common_1.Delete)('delete'),
    __param(0, (0, common_1.Body)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FileSystemController.prototype, "deleteItem", null);
__decorate([
    (0, common_1.Patch)('rename-file'),
    __param(0, (0, common_1.Body)('path')),
    __param(1, (0, common_1.Body)('newName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FileSystemController.prototype, "renameFile", null);
__decorate([
    (0, common_1.Patch)('rename-folder'),
    __param(0, (0, common_1.Body)('path')),
    __param(1, (0, common_1.Body)('newName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], FileSystemController.prototype, "renameFolder", null);
exports.FileSystemController = FileSystemController = __decorate([
    (0, common_1.Controller)('file-system')
], FileSystemController);
//# sourceMappingURL=file-upload.controller.js.map