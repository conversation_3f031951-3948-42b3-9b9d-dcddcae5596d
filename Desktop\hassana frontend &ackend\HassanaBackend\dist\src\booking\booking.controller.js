"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const booking_service_1 = require("./booking.service");
const create_booking_input_1 = require("./dto/create-booking.input");
let multerOptions = {
    storage: (0, multer_1.diskStorage)({
        destination: './v1/resource/booking',
        filename: (req, featuredImage, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = (0, path_1.extname)(featuredImage.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        },
    }),
};
let BookingController = class BookingController {
    constructor(bookingService) {
        this.bookingService = bookingService;
    }
    async createBooking(file, createBookingInput) {
        try {
            if (file) {
                let path = file?.path;
                var registrationDoc = path?.replace(/resource\/v1[\/\\]/g, "");
                ;
            }
            let data = await this.bookingService.create({
                ...createBookingInput,
                registrationDoc,
            });
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }
};
exports.BookingController = BookingController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('registrationDoc', multerOptions)),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_booking_input_1.CreateBookingInput]),
    __metadata("design:returntype", Promise)
], BookingController.prototype, "createBooking", null);
exports.BookingController = BookingController = __decorate([
    (0, common_1.Controller)('v1/our-booking'),
    __metadata("design:paramtypes", [booking_service_1.BookingService])
], BookingController);
//# sourceMappingURL=booking.controller.js.map