"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPaginationSchema = exports.UserMeta = void 0;
const graphql_1 = require("@nestjs/graphql");
const user_schema_1 = require("./user.schema");
let UserMeta = class UserMeta {
};
exports.UserMeta = UserMeta;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], UserMeta.prototype, "totalCount", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], UserMeta.prototype, "currentPage", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], UserMeta.prototype, "pageSize", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.Int),
    __metadata("design:type", Number)
], UserMeta.prototype, "totalPages", void 0);
exports.UserMeta = UserMeta = __decorate([
    (0, graphql_1.ObjectType)()
], UserMeta);
let UserPaginationSchema = class UserPaginationSchema {
};
exports.UserPaginationSchema = UserPaginationSchema;
__decorate([
    (0, graphql_1.Field)(() => [user_schema_1.UserSchema]),
    __metadata("design:type", Array)
], UserPaginationSchema.prototype, "users", void 0);
__decorate([
    (0, graphql_1.Field)(() => UserMeta),
    __metadata("design:type", UserMeta)
], UserPaginationSchema.prototype, "meta", void 0);
exports.UserPaginationSchema = UserPaginationSchema = __decorate([
    (0, graphql_1.ObjectType)()
], UserPaginationSchema);
//# sourceMappingURL=usersMeta.schema.js.map