"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject2());\n// Styled components for social media style notifications\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    sx: {\n                                                        p: 2,\n                                                        borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                        \"&:hover\": {\n                                                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        transition: \"all 0.2s ease-in-out\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                gap: 2,\n                                                                alignItems: \"flex-start\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        borderRadius: \"50%\",\n                                                                        //background: th,\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        justifyContent: \"center\",\n                                                                        flexShrink: 0,\n                                                                        boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                                        sx: {\n                                                                            color: \"white\",\n                                                                            fontSize: \"20px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        flex: 1,\n                                                                        minWidth: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"body1\",\n                                                                            sx: {\n                                                                                fontSize: \"14px\",\n                                                                                fontWeight: 500,\n                                                                                lineHeight: \"20px\",\n                                                                                color: \"#333\",\n                                                                                mb: 0.5,\n                                                                                wordBreak: \"break-word\"\n                                                                            },\n                                                                            children: notificationData.notification\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            sx: {\n                                                                                fontSize: \"12px\",\n                                                                                color: \"#666\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\",\n                                                                                gap: 0.5\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    sx: {\n                                                                                        fontSize: \"4px\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                position: \"absolute\",\n                                                                left: 0,\n                                                                top: 0,\n                                                                bottom: 0,\n                                                                width: \"3px\",\n                                                                //background:\n                                                                //  \"linear-gradient(180deg, #667eea 0%, #764ba2 100%)\",\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, notificationData.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 375,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            gap: 1,\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 399,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 456,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showBadgeCount, setShowBadgeCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    // Derive userId from session\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    // Handle session loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 478,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 477,\n            columnNumber: 7\n        }, this);\n    }\n    // Query for new notifications\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNewNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    // Notification sound function\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    // Show browser notification\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    // Request notification permission\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    // Debug session object\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    // Handle notification query results\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.newNotificationsForUser);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.newNotificationsForUser)) {\n            const newNotifications = data.newNotificationsForUser;\n            const currentCount = newNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"Notifications received:\", newNotifications);\n            console.log(\"Count:\", currentCount);\n            console.log(\"showBadgeCount state:\", showBadgeCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 3000);\n            }\n            setNotifications(newNotifications);\n            setPreviousNotificationCount(currentCount);\n            // Only show badge count if there are notifications and the panel is not currently open\n            if (currentCount > 0 && !notificationOpen) {\n                setShowBadgeCount(true);\n                console.log(\"Setting showBadgeCount to TRUE because count > 0 and panel is closed\");\n            } else if (currentCount === 0) {\n                setShowBadgeCount(false);\n                console.log(\"Setting showBadgeCount to FALSE because no notifications\");\n            }\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            // Retry polling after 30 seconds\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        // Immediately hide the badge count when opening the notification panel\n        if (!wasOpen) {\n            setShowBadgeCount(false);\n            // Mark all notifications as viewed after a short delay to allow user to see them\n            if (notifications.length > 0) {\n                setTimeout(()=>{\n                    markAllNotificationsAsViewed();\n                }, 1500);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        // Keep badge count hidden since notifications were already seen when panel was opened\n        setShowBadgeCount(false);\n    };\n    const markAllNotificationsAsViewed = async ()=>{\n        if (!userId || notifications.length === 0) return;\n        try {\n            const promises = notifications.map((notification)=>addNotificationView({\n                    variables: {\n                        notificationId: notification.id,\n                        user_id: userId\n                    }\n                }));\n            await Promise.all(promises);\n            // Clear notifications and reset counts\n            setNotifications([]);\n            setPreviousNotificationCount(0);\n            setShowBadgeCount(false);\n            console.log(\"All notifications marked as viewed and cleared\");\n        } catch (error) {\n            console.error(\"Error marking all notifications as viewed:\", error);\n        }\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                // Remove the notification from the list immediately\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                // Update badge count to show only unseen notifications\n                setShowBadgeCount(true);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 752,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 759,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 745,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 770,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 769,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 762,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: '\"\"',\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 810,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 792,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"blue\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"blue\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"green\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"green\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"purple\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"purple\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 838,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 837,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 828,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"6px\",\n                                                    sm: \"8px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: showBadgeCount ? notifications.length : 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8pxrgb(122, 15, 15))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 907,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 891,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 890,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 929,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 779,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 732,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 731,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid #EBEBEB\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 968,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 959,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 981,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 985,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 990,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1002,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1008,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1012,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1018,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1022,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1028,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1027,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1025,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1038,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1036,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1035,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1048,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1057,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1062,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 980,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1071,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 970,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 951,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 943,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1081,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1076,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Ra7y/5uHrJet5+UvmlxfSvo3ne4=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});