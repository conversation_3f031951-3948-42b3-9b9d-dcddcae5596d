"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserReviewsController = void 0;
const common_1 = require("@nestjs/common");
const user_reviews_service_1 = require("./user-reviews.service");
const create_user_review_dto_1 = require("./dto/create-user-review.dto");
const update_user_review_dto_1 = require("./dto/update-user-review.dto");
const jwt_guard_1 = require("../auth/jwt.guard");
const swagger_1 = require("@nestjs/swagger");
let UserReviewsController = class UserReviewsController {
    constructor(userReviewsService) {
        this.userReviewsService = userReviewsService;
    }
    async create(createUserReviewDto, request) {
        try {
            let { id } = request['user'];
            let data = await this.userReviewsService.create(createUserReviewDto, id);
            return {
                status: true,
                message: "Success",
                data: data
            };
        }
        catch (error) {
            console.log(error);
            return {
                status: false,
                message: "Internal Server Error",
                errorMessage: error.message
            };
        }
    }
    async findAll() {
        try {
            let data = await this.userReviewsService.findAll();
            return {
                status: true,
                message: "Success",
                data: data
            };
        }
        catch (error) {
            return {
                status: false,
                message: "Internal Server Error",
                errorMessage: error.message
            };
        }
    }
    findOne(id) {
        return this.userReviewsService.findOne(+id);
    }
    update(id, updateUserReviewDto) {
        return this.userReviewsService.update(+id, updateUserReviewDto);
    }
    remove(id) {
        return this.userReviewsService.remove(+id);
    }
};
exports.UserReviewsController = UserReviewsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiBody)({ type: create_user_review_dto_1.CreateUserReviewDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_review_dto_1.CreateUserReviewDto, Request]),
    __metadata("design:returntype", Promise)
], UserReviewsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserReviewsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserReviewsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_review_dto_1.UpdateUserReviewDto]),
    __metadata("design:returntype", void 0)
], UserReviewsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserReviewsController.prototype, "remove", null);
exports.UserReviewsController = UserReviewsController = __decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, common_1.Controller)('v1/user-reviews'),
    __metadata("design:paramtypes", [user_reviews_service_1.UserReviewsService])
], UserReviewsController);
//# sourceMappingURL=user-reviews.controller.js.map