"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const fs_1 = require("fs");
const path = require("path");
const typeorm_2 = require("typeorm");
const news_entity_1 = require("./entities/news.entity");
let NewsService = class NewsService {
    constructor(newsRepository) {
        this.newsRepository = newsRepository;
    }
    validateUUID(id) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(id)) {
            throw new Error('Invalid UUID format');
        }
        return id;
    }
    async createNews(createNewsInput) {
        try {
            const res = await this.newsRepository.save(createNewsInput);
            if (res.featuredImage) {
                res.featuredImage = `${process.env.SERVER_URL}/${res.featuredImage}`;
            }
            return res;
        }
        catch (error) {
            throw error;
        }
    }
    async allNews() {
        const newsList = await this.newsRepository.find();
        if (newsList && newsList.length > 0) {
            newsList.forEach(news => {
                if (news.featuredImage) {
                    news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
                }
                console.log(news.featuredImage);
            });
        }
        return newsList;
    }
    async allExternalNews() {
        const todayStart = new Date();
        todayStart.setUTCHours(0, 0, 0, 0);
        const newsList = await this.newsRepository.find({
            where: {
                status: "true",
                category: "external",
                visibility: (0, typeorm_2.MoreThanOrEqual)(todayStart)
            }
        });
        if (newsList && newsList.length > 0) {
            newsList.forEach(news => {
                if (news.featuredImage) {
                    news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
                }
            });
        }
        return newsList;
    }
    async allInternalNews() {
        const todayStart = new Date();
        todayStart.setUTCHours(0, 0, 0, 0);
        const newsList = await this.newsRepository.find({
            where: {
                status: "true",
                category: "internal",
                visibility: (0, typeorm_2.MoreThanOrEqual)(todayStart)
            }
        });
        if (newsList && newsList.length > 0) {
            newsList.forEach(news => {
                if (news.featuredImage) {
                    news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
                }
            });
        }
        return newsList;
    }
    async findOne(id) {
        const validId = this.validateUUID(id);
        const news = await this.newsRepository.findOne({ where: { id: validId } });
        if (news && news.featuredImage) {
            news.featuredImage = `${process.env.SERVER_URL}/${news.featuredImage}`;
        }
        return news;
    }
    async update(id, updateNewsInput) {
        try {
            const validId = this.validateUUID(id);
            const existingNews = await this.newsRepository.findOne({ where: { id: validId } });
            if (existingNews) {
                console.log("image checking for delete", updateNewsInput.featuredImage);
                if (updateNewsInput.featuredImage != undefined) {
                    const imageInServer = path.join(__dirname, "../../resource/" + existingNews.featuredImage);
                    if ((0, fs_1.existsSync)(imageInServer)) {
                        console.log("deleting Image in server ...: " + imageInServer);
                        (0, fs_1.unlinkSync)(imageInServer);
                    }
                }
                this.newsRepository.merge(existingNews, updateNewsInput);
                existingNews.updated_on = new Date();
                let updateData = await this.newsRepository.save(existingNews);
                if (updateData && updateData.featuredImage) {
                    updateData.featuredImage = `${process.env.SERVER_URL}/${updateData.featuredImage}`;
                }
                return updateData;
            }
        }
        catch (error) {
            throw error;
        }
    }
    async remove(id) {
        const validId = this.validateUUID(id);
        const newsToRemove = await this.newsRepository.findOne({ where: { id: validId } });
        if (!newsToRemove) {
            throw new common_1.NotFoundException('News not found');
        }
        if (newsToRemove) {
            try {
                if (newsToRemove.featuredImage) {
                    const imagePath = path.join(__dirname, "../../resource/" + newsToRemove.featuredImage);
                    if ((0, fs_1.existsSync)(imagePath)) {
                        console.log("deleting Image in server ...: " + imagePath);
                        (0, fs_1.unlinkSync)(imagePath);
                    }
                }
                await this.newsRepository.remove(newsToRemove);
                return newsToRemove;
            }
            catch (error) {
                throw error;
            }
        }
        return null;
    }
};
exports.NewsService = NewsService;
exports.NewsService = NewsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(news_entity_1.News)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], NewsService);
//# sourceMappingURL=news.service.js.map