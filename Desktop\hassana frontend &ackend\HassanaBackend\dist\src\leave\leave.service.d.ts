import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { Leave } from './entities/leave.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class LeaveService {
    private readonly leaveRepository;
    constructor(leaveRepository: Repository<Leave>);
    create(createLeaveInput: CreateLeaveInput): Promise<Leave>;
    findAll(): Promise<Leave[]>;
    findOne(id: UUID): Promise<Leave>;
    userLeave(id: number): Promise<{
        medical: number;
        casual: number;
    }>;
    update(id: UUID, updateLeaveInput: UpdateLeaveInput): Promise<Leave>;
    remove(id: UUID): Promise<Leave | null>;
}
