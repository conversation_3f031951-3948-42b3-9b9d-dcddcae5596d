import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementInput } from './dto/create-announcement.input';
import { UpdateAnnouncementInput } from './dto/update-announcement.input';
import { UUID } from 'crypto';
export declare class AnnouncementController {
    private readonly announcementService;
    constructor(announcementService: AnnouncementService);
    createAnnouncement(file: Express.Multer.File, createAnnouncementInput: CreateAnnouncementInput): Promise<{
        code: number;
        message: string;
        data: import("./entities/announcement.entity").Announcement;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    findAllAnnouncements(req: Request): Promise<{
        code: number;
        message: string;
        data: import("./entities/announcement.entity").Announcement[];
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    createAnnouncementView(announcement_id: UUID, req: Request): Promise<{
        code: number;
        message: string;
        data: import("./entities/announcement.entity").Announcement;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    findOneAnnouncement(id: UUID): Promise<{
        code: number;
        message: string;
        data: import("./entities/announcement.entity").Announcement;
    } | {
        code: any;
        mesage: any;
        error: any;
    }>;
    updateAnnouncement(id: UUID, updateAnnouncementInput: UpdateAnnouncementInput, file: Express.Multer.File): Promise<{
        code: number;
        message: string;
        data: UpdateAnnouncementInput;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    removeAnnouncement(id: UUID): Promise<{
        code: number;
        message: string;
        data: import("./entities/announcement.entity").Announcement;
    } | {
        code: any;
        mesage: any;
        error: any;
    }>;
}
