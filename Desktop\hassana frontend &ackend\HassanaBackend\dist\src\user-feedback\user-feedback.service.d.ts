import { CreateUserFeedbackDto } from './dto/create-user-feedback.dto';
import { UserFeedback } from './entities/user-feedback.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class UserFeedbackService {
    private feedbackRepository;
    constructor(feedbackRepository: Repository<UserFeedback>);
    create(createUserFeedbackDto: CreateUserFeedbackDto, userId: UUID): Promise<UserFeedback>;
    findAll(): Promise<UserFeedback[]>;
    findOne(id: UUID): Promise<UserFeedback>;
    remove(id: UUID): Promise<UserFeedback>;
}
