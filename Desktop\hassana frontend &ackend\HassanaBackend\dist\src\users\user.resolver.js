"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const user_service_1 = require("./user.service");
const user_schema_1 = require("./schema/user.schema");
const jwt = require("jsonwebtoken");
const login_user_1 = require("./dto/login-user");
const common_1 = require("@nestjs/common");
const redis_1 = require("../../redis");
const usersMeta_schema_1 = require("./schema/usersMeta.schema");
let UserResolver = class UserResolver {
    constructor(userService) {
        this.userService = userService;
    }
    async getAllUsers(page, pageSize) {
        try {
            const { users, meta } = await this.userService.findAllUsers(page, pageSize);
            return { users, meta };
        }
        catch (error) {
            return error;
        }
        ;
    }
    getNewUsers(days) {
        return this.userService.findNewUsers(days);
    }
    async getCulturalAmbassadors() {
        return await this.userService.findAllCulturalAmbassadors();
    }
    async loginUser(username, password) {
        try {
            let userAuthenticationResult = await this.userService.userAuth(username, password);
            common_1.Logger.log("User Authentication: ", userAuthenticationResult);
            if (userAuthenticationResult) {
                let user;
                user = await this.userService.findUserByEmail(userAuthenticationResult.userPrincipalName);
                if (!user?.id) {
                    common_1.Logger.log('User not found...');
                    user = await this.userService.createNewUser(userAuthenticationResult);
                    common_1.Logger.log("Created new user > ");
                }
                await redis_1.redis.hset(`user: ${user.id}`, 'password', password);
                await redis_1.redis.hset(`user: ${user.id}`, 'username', username);
                let redisValue = await redis_1.redis.hgetall(`user: ${user.id}`);
                common_1.Logger.log('Get all hashes', redisValue);
                let tokenPayload = {
                    id: user.id,
                    username: user.name,
                    role: user.role,
                };
                const KEY = process.env.JWT_KEY;
                const token = jwt.sign(tokenPayload, KEY, {});
                let payload = {
                    id: user.id,
                    username: user.name,
                    role: user.role,
                    token: token,
                };
                return payload;
            }
            else {
                common_1.Logger.log('Authentication failed...');
                throw new Error('Authentication failed');
            }
        }
        catch (error) {
            common_1.Logger.error('An error occurred:', error);
            console.log(error);
            throw new Error('Server Error');
        }
    }
    findUserById(id) {
        return this.userService.findUserById(id);
    }
};
exports.UserResolver = UserResolver;
__decorate([
    (0, graphql_1.Query)(() => usersMeta_schema_1.UserPaginationSchema, { name: 'users' }),
    __param(0, (0, graphql_1.Args)('page')),
    __param(1, (0, graphql_1.Args)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UserResolver.prototype, "getAllUsers", null);
__decorate([
    (0, graphql_1.Query)((returns) => [user_schema_1.UserSchema]),
    __param(0, (0, graphql_1.Args)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], UserResolver.prototype, "getNewUsers", null);
__decorate([
    (0, graphql_1.Query)((returns) => [user_schema_1.UserSchema]),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserResolver.prototype, "getCulturalAmbassadors", null);
__decorate([
    (0, graphql_1.Query)((returns) => login_user_1.LoginUser),
    __param(0, (0, graphql_1.Args)('username')),
    __param(1, (0, graphql_1.Args)('password')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserResolver.prototype, "loginUser", null);
__decorate([
    (0, graphql_1.Query)(() => user_schema_1.UserSchema),
    __param(0, (0, graphql_1.Args)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserResolver.prototype, "findUserById", null);
exports.UserResolver = UserResolver = __decorate([
    (0, graphql_1.Resolver)(() => user_schema_1.UserSchema),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserResolver);
//# sourceMappingURL=user.resolver.js.map