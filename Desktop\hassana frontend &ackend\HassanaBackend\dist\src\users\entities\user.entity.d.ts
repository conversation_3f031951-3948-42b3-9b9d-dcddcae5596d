import { BaseEntity } from '@app/BaseEntity';
import { NotificationViewEntity } from '@app/notification/entities/notification.entity';
import { UserReview } from '@app/user-reviews/entities/user-review.entity';
export declare enum RolesEnum {
    ADMIN = "Admin",
    USER = "User"
}
export declare class User extends BaseEntity {
    profile: string;
    email: string;
    name: string;
    name_arabic: string;
    designation: string;
    designation_arabic: string;
    department: string;
    department_arabic: string;
    bio_link: string;
    dn: string;
    gender: string;
    new_joiner: string;
    account_expires?: string;
    user_principal_name: string;
    role: string;
    status: string;
    is_cultural_ambassador: string;
    activity: string;
    extension: string;
    user_reviews: UserReview[];
    notification_view: NotificationViewEntity[];
}
