import { Repository } from 'typeorm';
import { UpdateBookingInput } from './dto/update-booking.input';
import { Booking } from './entities/booking.entity';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';
export declare class BookingService {
    private bookingRepository;
    private userRepository;
    constructor(bookingRepository: Repository<Booking>, userRepository: Repository<User>);
    findAll(): Promise<Booking[]>;
    getAllBookingTeaBoy(): Promise<Booking[]>;
    findByUser(id: UUID): Promise<Booking[]>;
    create(updateBookingInput: UpdateBookingInput): Promise<Booking>;
}
