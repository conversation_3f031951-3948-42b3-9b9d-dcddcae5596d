"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFeedback = exports.FeedbackType = void 0;
const BaseEntity_1 = require("../../BaseEntity");
const user_entity_1 = require("../../users/entities/user.entity");
const graphql_1 = require("@nestjs/graphql");
const typeorm_1 = require("typeorm");
var FeedbackType;
(function (FeedbackType) {
    FeedbackType["COMPLAINT"] = "complaint";
    FeedbackType["SUGGESTION"] = "suggestion";
})(FeedbackType || (exports.FeedbackType = FeedbackType = {}));
let UserFeedback = class UserFeedback extends BaseEntity_1.BaseEntity {
};
exports.UserFeedback = UserFeedback;
__decorate([
    (0, graphql_1.Field)(),
    (0, typeorm_1.Column)({
        type: "enum",
        default: FeedbackType.SUGGESTION,
        enum: FeedbackType
    }),
    __metadata("design:type", String)
], UserFeedback.prototype, "type", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], UserFeedback.prototype, "subject", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], UserFeedback.prototype, "description", void 0);
__decorate([
    (0, graphql_1.Field)(),
    (0, typeorm_1.Column)(),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: "user_id" }),
    __metadata("design:type", String)
], UserFeedback.prototype, "user_id", void 0);
exports.UserFeedback = UserFeedback = __decorate([
    (0, typeorm_1.Entity)()
], UserFeedback);
//# sourceMappingURL=user-feedback.entity.js.map