"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaveService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const leave_entity_1 = require("./entities/leave.entity");
const typeorm_2 = require("typeorm");
let LeaveService = class LeaveService {
    constructor(leaveRepository) {
        this.leaveRepository = leaveRepository;
    }
    async create(createLeaveInput) {
        const newLeave = this.leaveRepository.create({
            ...createLeaveInput
        });
        const savedLeave = await this.leaveRepository.save(newLeave);
        return savedLeave;
    }
    findAll() {
        return this.leaveRepository.find();
    }
    findOne(id) {
        return this.leaveRepository.findOne({ where: { id } });
    }
    async userLeave(id) {
        try {
            const currentYear = new Date().getFullYear();
            const startDate = `${currentYear}-01-01`;
            const endDate = `${currentYear}-12-31`;
            const medicalCount = await this.leaveRepository
                .createQueryBuilder()
                .select('SUM("Leave"."numberOfDays")', 'totalMedicalDays')
                .where('"Leave"."userId" = :id', { id })
                .andWhere('"Leave"."date" BETWEEN :startDate AND :endDate', { startDate, endDate })
                .andWhere('"Leave"."typeOfLeave" = :type', { type: 'medical' })
                .getRawOne();
            const casualCount = await this.leaveRepository
                .createQueryBuilder()
                .select('SUM("Leave"."numberOfDays")', 'totalCasualDays')
                .where('"Leave"."userId" = :id', { id })
                .andWhere('"Leave"."date" BETWEEN :startDate AND :endDate', { startDate, endDate })
                .andWhere('"Leave"."typeOfLeave" = :type', { type: 'casual' })
                .getRawOne();
            const totalMedicalDays = medicalCount.totalMedicalDays || 0;
            const totalCasualDays = casualCount.totalCasualDays || 0;
            return { medical: totalMedicalDays, casual: totalCasualDays };
        }
        catch (error) {
            console.error('Error fetching leave counts for user:', error);
        }
    }
    async update(id, updateLeaveInput) {
        const existingLeave = await this.leaveRepository.findOne({ where: { id } });
        if (existingLeave) {
            this.leaveRepository.merge(existingLeave, updateLeaveInput);
            return this.leaveRepository.save(existingLeave);
        }
        return null;
    }
    async remove(id) {
        const leaveToRemove = await this.leaveRepository.findOne({ where: { id } });
        if (leaveToRemove) {
            await this.leaveRepository.remove(leaveToRemove);
            return leaveToRemove;
        }
        return null;
    }
};
exports.LeaveService = LeaveService;
exports.LeaveService = LeaveService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(leave_entity_1.Leave)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LeaveService);
//# sourceMappingURL=leave.service.js.map