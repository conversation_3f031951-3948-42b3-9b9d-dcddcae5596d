"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaveResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const leave_service_1 = require("./leave.service");
const leave_schema_1 = require("./schema/leave.schema");
const create_leave_input_1 = require("./dto/create-leave.input");
const update_leave_input_1 = require("./dto/update-leave.input");
const leaveCount_1 = require("./dto/leaveCount");
let LeaveResolver = class LeaveResolver {
    constructor(leaveService) {
        this.leaveService = leaveService;
    }
    createLeave(createLeaveInput) {
        return this.leaveService.create(createLeaveInput);
    }
    findAll() {
        return this.leaveService.findAll();
    }
    getUserLeaves(id) {
        return this.leaveService.userLeave(id);
    }
    findOne(id) {
        return this.leaveService.findOne(id);
    }
    updateLeave(updateLeaveInput) {
        return this.leaveService.update(updateLeaveInput.id, updateLeaveInput);
    }
    removeLeave(id) {
        return this.leaveService.remove(id);
    }
};
exports.LeaveResolver = LeaveResolver;
__decorate([
    (0, graphql_1.Mutation)(() => leave_schema_1.Leave),
    __param(0, (0, graphql_1.Args)('createLeaveInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_leave_input_1.CreateLeaveInput]),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "createLeave", null);
__decorate([
    (0, graphql_1.Query)(() => [leave_schema_1.Leave], { name: 'leaves' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "findAll", null);
__decorate([
    (0, graphql_1.Query)(() => leaveCount_1.LeaveCount, { name: 'getUserLeaves' }),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "getUserLeaves", null);
__decorate([
    (0, graphql_1.Query)(() => leave_schema_1.Leave, { name: 'leave' }),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "findOne", null);
__decorate([
    (0, graphql_1.Mutation)(() => leave_schema_1.Leave),
    __param(0, (0, graphql_1.Args)('updateLeaveInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_leave_input_1.UpdateLeaveInput]),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "updateLeave", null);
__decorate([
    (0, graphql_1.Mutation)(() => leave_schema_1.Leave),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LeaveResolver.prototype, "removeLeave", null);
exports.LeaveResolver = LeaveResolver = __decorate([
    (0, graphql_1.Resolver)(() => leave_schema_1.Leave),
    __metadata("design:paramtypes", [leave_service_1.LeaveService])
], LeaveResolver);
//# sourceMappingURL=leave.resolver.js.map