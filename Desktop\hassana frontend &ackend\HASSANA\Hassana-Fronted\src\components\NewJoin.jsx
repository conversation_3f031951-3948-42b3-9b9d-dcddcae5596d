import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ade,
  Box,
  Typography,
  Avatar,
  CircularProgress,
  Button,
} from "@mui/material";
import { useTheme } from "@mui/system";
import { getAllUsers } from "@/Data/User";
import { Grid } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useSession } from "next-auth/react";
import UserCard from "./UserCard";
import { lightTheme } from "@/theme";

// Define breakpoints for responsive design
const breakpoints = {
  smallScreen: "(max-width: 600px)",
  mediumScreen: "(max-width: 960px)",
  largeScreen: "(max-width: 1280px)",
  xLargeScreen: "(max-width: 1440px)",
  xxLargeScreen: "(max-width: 1920px)",
  xxxLargeScreen: "(max-width: 2560px)",
  xxxxLargeScreen: "(min-width: 2561px)",
};

// JoinerBox component to display users
const JoinerBox = ({ title, subtitle, users, loading, error }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState({});
  const [userCardOpen, setUserCardOpen] = useState({});
  const [showAllUsers, setShowAllUsers] = useState(false);
  const Baseurl = 'https://hassana-api.360xpertsolutions.com/v1/'

  const handlePopoverOpen = (event, index) => {
    setAnchorEl((prev) => ({ ...prev, [index]: event.currentTarget }));
  };

  const handlePopoverClose = (index) => {
    setAnchorEl((prev) => ({ ...prev, [index]: null }));
  };

  const visibleUsers = showAllUsers ? users : users.slice(0, 4);
  const extraUsersCount = users.length - 4;

  return (
    <Box
      sx={{
        background: theme.palette.background.secondary,
        borderRadius: "10px",
        boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
        padding: { xs: "15px", sm: "20px" },
        cursor: "default",
        minHeight: { xs: "180px", sm: "200px", md: "220px" },
        height: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontSize: { xs: "1.1rem", sm: "1.2rem", md: "1.3rem" },
          fontWeight: "700",
          lineHeight: 1.2,
        }}
      >
        {title}
      </Typography>
      <Typography
        sx={{
          fontSize: { xs: "0.85rem", sm: "0.9rem", md: "1rem" },
          fontWeight: "100",
          lineHeight: 1.3,
          marginTop: { xs: "5px", sm: "8px" },
        }}
      >
        {subtitle}
      </Typography>

      <Box sx={{
        marginTop: { xs: "15px", sm: "20px" },
        flex: 1,
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", flex: 1 }}>
            <CircularProgress color="secondary" />
          </Box>
        ) : error ? (
          <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", flex: 1 }}>
            <Typography color="error" sx={{ marginY: "10px", fontSize: { xs: "0.9rem", sm: "1rem" } }}>
              Error: {error}
            </Typography>
          </Box>
        ) : users && users.length > 0 ? (
          <>
            <Box
              sx={{
                display: "flex",
                overflowX: showAllUsers ? "auto" : "visible",
                gap: { xs: 0.5, sm: 1 },
                maxWidth: "100%",
                flexWrap: showAllUsers ? "wrap" : "nowrap",
                justifyContent: { xs: "flex-start", sm: "flex-start" },
              }}
            >
              {visibleUsers.map((user, index) => (
                <React.Fragment key={user.id}>
                  <Avatar
                    alt={user.name}
                    src={Baseurl+user.profile || "/default-avatar.png"}
                    onMouseEnter={(e) => handlePopoverOpen(e, index)}
                    onMouseLeave={() => handlePopoverClose(index)}
                    onClick={() =>
                      setUserCardOpen((prev) => ({ ...prev, [index]: true }))
                    }
                    sx={{
                      cursor: "pointer",
                      flexShrink: 0,
                      width: { xs: 35, sm: 40, md: 45 },
                      height: { xs: 35, sm: 40, md: 45 },
                    }}
                  />
                  <Popper
                    open={Boolean(anchorEl[index])}
                    anchorEl={anchorEl[index]}
                    placement="top"
                    transition
                    disablePortal
                  >
                    {({ TransitionProps }) => (
                      <Fade {...TransitionProps} timeout={350}>
                        <Box
                          sx={{
                            border: 1,
                            padding: "8px",
                            backgroundColor: theme.palette.background.primary,
                            borderRadius: "8px",
                          }}
                        >
                          <Typography>{user.name}</Typography>
                        </Box>
                      </Fade>
                    )}
                  </Popper>
                  <UserCard
                    open={userCardOpen[index]}
                    setOpen={(value) =>
                      setUserCardOpen((prev) => ({
                        ...prev,
                        [index]: value,
                      }))
                    }
                    data={user}
                  />
                </React.Fragment>
              ))}

              {!showAllUsers && extraUsersCount > 0 && (
                <Avatar
                  sx={{
                    bgcolor: "#aaa",
                    cursor: "pointer",
                    flexShrink: 0,
                    width: { xs: 35, sm: 40, md: 45 },
                    height: { xs: 35, sm: 40, md: 45 },
                    fontSize: { xs: "0.8rem", sm: "0.9rem", md: "1rem" },
                  }}
                  onClick={() => setShowAllUsers(true)}
                >
                  +{extraUsersCount}
                </Avatar>
              )}
            </Box>
            {showAllUsers && extraUsersCount > 0 && (
              <Button
                onClick={() => setShowAllUsers(false)}
                sx={{ marginTop: "10px", color: "#A665E1" }}
              >
                Show Less
              </Button>
            )}
          </>
        ) : (
          <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", flex: 1 }}>
            <Typography
              variant="h5"
              sx={{
                marginY: "10px",
                fontSize: { xs: "1rem", sm: "1.2rem", md: "1.5rem" },
                color: "text.secondary",
              }}
            >
              No data found
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

// Main NewJoin component
const NewJoin = () => {
  const [newJoiners, setNewJoiners] = useState([]);
  const [culturalAmbassadors, setCulturalAmbassadors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { data: session, status: sessionStatus } = useSession();

  // Responsive breakpoints
  const isSmallScreen = useMediaQuery(breakpoints.smallScreen);
  const isMediumScreen = useMediaQuery(breakpoints.mediumScreen);
  const isLargeScreen = useMediaQuery(breakpoints.largeScreen);
  const isXLargeScreen = useMediaQuery(breakpoints.xLargeScreen);
  const isXXLargeScreen = useMediaQuery(breakpoints.xxLargeScreen);
  const isXXXLargeScreen = useMediaQuery(breakpoints.xxxLargeScreen);
  const isXXXXLargeScreen = useMediaQuery(breakpoints.xxxxLargeScreen);

  // Set main div width based on screen size
  let mainDivWidth;
  if (isSmallScreen) {
    mainDivWidth = "90vw";
  } else if (isMediumScreen) {
    mainDivWidth = "80vw";
  } else if (isLargeScreen) {
    mainDivWidth = "77vw";
  } else if (isXLargeScreen) {
    mainDivWidth = "54vw";
  } else if (isXXLargeScreen) {
    mainDivWidth = "62vw";
  } else if (isXXXLargeScreen) {
    mainDivWidth = "67vw";
  } else if (isXXXXLargeScreen) {
    mainDivWidth = "70vw";
  } else {
    mainDivWidth = "70vw";
  }

  useEffect(() => {
    const fetchUsers = async () => {
      if (sessionStatus === "loading" || !session?.accessToken) return;

      setLoading(true);
      setError(null);

      try {
        const response = await getAllUsers(session.accessToken, 1, 10);
        if (response.error) {
          setError(response.error);
          setNewJoiners([]);
          setCulturalAmbassadors([]);
        } else {
          const users = response.data || []; // Adjust if response is nested (e.g., response.data.users)
          const newJoiners = users.filter(
            (user) => user.is_cultural_ambassador !== "true"
          );
          const culturalAmbassadors = users.filter(
            (user) => user.is_cultural_ambassador === "true"
          );
          setNewJoiners(newJoiners);
          setCulturalAmbassadors(culturalAmbassadors);
        }
      } catch (err) {
        setError(err.message || "Failed to fetch users");
        setNewJoiners([]);
        setCulturalAmbassadors([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [session, sessionStatus]);

  return (
    <Box sx={{ marginY: "20px", }}>
      {error && (
        <Typography color="error" sx={{ textAlign: "center", marginY: "10px" }}>
          Error: {error}
        </Typography>
      )}
      <Grid container spacing={isSmallScreen ? 1 : 3}>
        <Grid item xs={12} sm={12} md={6} lg={6}
        sx={{cursor: "default"}}>
          <JoinerBox
            title="Welcome Hassana New Joiners"
            subtitle="Welcome Hassana and our new team members! We're excited to start this journey together."
            users={newJoiners}
            loading={loading}
            error={error}
          />
        </Grid>
        <Grid item xs={12} sm={12} md={6} lg={6}>
          <JoinerBox
            title="Cultural Ambassadors"
            subtitle="Celebrating our team members who promote our culture and values."
            users={culturalAmbassadors}
            loading={loading}
            error={error}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default NewJoin;