"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const dotenv = require("dotenv");
const path = require("path");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
dotenv.config();
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, { cors: true });
    app.useLogger(new common_1.Logger());
    app.useStaticAssets(path.join(__dirname, '../../resource'));
    app.useStaticAssets(path.join(__dirname, '../../library'));
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        forbidUnknownValues: false,
        transformOptions: { enableImplicitConversion: true },
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Hassana APIs Document')
        .setVersion('1.0')
        .build();
    const documentFactory = () => swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('/v1/docs', app, documentFactory);
    app.enableCors({
        origin: ['*'],
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        preflightContinue: true,
        optionsSuccessStatus: 204,
        credentials: true,
    });
    await app.listen(process.env.SERVER_PORT, () => {
        common_1.Logger.log("Server is running on port: " + process.env.SERVER_PORT);
    });
}
bootstrap();
//# sourceMappingURL=main.js.map