"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject2());\n// Styled components for social media style notifications\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    onClick: ()=>removeHandler(notificationData.id),\n                                                    sx: {\n                                                        p: 2,\n                                                        borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                        \"&:hover\": {\n                                                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        transition: \"all 0.2s ease-in-out\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                gap: 2,\n                                                                alignItems: \"flex-start\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        borderRadius: \"50%\",\n                                                                        //background: th,\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        justifyContent: \"center\",\n                                                                        flexShrink: 0,\n                                                                        boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                                        sx: {\n                                                                            color: \"white\",\n                                                                            fontSize: \"20px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        flex: 1,\n                                                                        minWidth: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"body1\",\n                                                                            sx: {\n                                                                                fontSize: \"14px\",\n                                                                                fontWeight: 500,\n                                                                                lineHeight: \"20px\",\n                                                                                color: \"#333\",\n                                                                                mb: 0.5,\n                                                                                wordBreak: \"break-word\"\n                                                                            },\n                                                                            children: notificationData.notification\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            sx: {\n                                                                                fontSize: \"12px\",\n                                                                                color: \"#666\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\",\n                                                                                gap: 0.5\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    sx: {\n                                                                                        fontSize: \"4px\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                position: \"absolute\",\n                                                                left: 0,\n                                                                top: 0,\n                                                                bottom: 0,\n                                                                width: \"3px\",\n                                                                //background:\n                                                                //  \"linear-gradient(180deg, #667eea 0%, #764ba2 100%)\",\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, notificationData.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            gap: 1,\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 457,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showBadgeCount, setShowBadgeCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    // Derive userId from session\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    // Handle session loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 479,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 478,\n            columnNumber: 7\n        }, this);\n    }\n    // Query for new notifications\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNewNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    // Notification sound function\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    // Show browser notification\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    // Request notification permission\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    // Debug session object\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    // Handle notification query results\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.newNotificationsForUser);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.newNotificationsForUser)) {\n            const newNotifications = data.newNotificationsForUser;\n            const currentCount = newNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"Notifications received:\", newNotifications);\n            console.log(\"Count:\", currentCount);\n            console.log(\"showBadgeCount state:\", showBadgeCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 3000);\n            }\n            setNotifications(newNotifications);\n            setPreviousNotificationCount(currentCount);\n            // Only show badge count if there are notifications and the panel is not currently open\n            if (currentCount > 0 && !notificationOpen) {\n                setShowBadgeCount(true);\n                console.log(\"Setting showBadgeCount to TRUE because count > 0 and panel is closed\");\n            } else if (currentCount === 0) {\n                setShowBadgeCount(false);\n                console.log(\"Setting showBadgeCount to FALSE because no notifications\");\n            }\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            // Retry polling after 30 seconds\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = ()=>{\n        setNotificationOpen((prev)=>!prev);\n    // Don't automatically mark notifications as viewed when opening the panel\n    // Users need to click on individual notifications to mark them as seen\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        // Keep badge count hidden since notifications were already seen when panel was opened\n        setShowBadgeCount(false);\n    };\n    const markAllNotificationsAsViewed = async ()=>{\n        if (!userId || notifications.length === 0) return;\n        try {\n            const promises = notifications.map((notification)=>addNotificationView({\n                    variables: {\n                        notificationId: notification.id,\n                        user_id: userId\n                    }\n                }));\n            await Promise.all(promises);\n            // Clear notifications and reset counts\n            setNotifications([]);\n            setPreviousNotificationCount(0);\n            setShowBadgeCount(false);\n            console.log(\"All notifications marked as viewed and cleared\");\n        } catch (error) {\n            console.error(\"Error marking all notifications as viewed:\", error);\n        }\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                // Remove the notification from the list immediately\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                // Update badge count to show only unseen notifications\n                setShowBadgeCount(true);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 742,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 749,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 760,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 759,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 752,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: '\"\"',\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 800,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 782,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"blue\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"blue\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"green\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"green\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"purple\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"purple\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 828,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"6px\",\n                                                    sm: \"8px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: notifications.length,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8pxrgb(122, 15, 15))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 897,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 881,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 880,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 879,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 769,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 721,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid #EBEBEB\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 958,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 949,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 971,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 988,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 992,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1002,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1008,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1012,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1018,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1017,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1022,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1015,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1028,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1027,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1025,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1038,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1036,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1035,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1048,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 970,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1061,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 960,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 941,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 933,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1071,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1066,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Ra7y/5uHrJet5+UvmlxfSvo3ne4=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});