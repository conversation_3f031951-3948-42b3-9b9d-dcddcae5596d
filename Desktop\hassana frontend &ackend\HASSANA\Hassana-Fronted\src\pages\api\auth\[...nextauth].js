import authclient from "@/Data/AuthClientServer";
import { LOGIN_USER } from "@/Data/Auth";
import { client } from "@/Data/ApolloClient";

import NextAuth from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";

export default NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { username, password } = credentials;

        try {
          await authclient.resetCache();
          const { data, errors } = await authclient.query({
            query: LOGIN_USER,
            variables: { username, password },
          });

          const { loginUser } = data;

          if (loginUser) {
            console.log(loginUser, "Login");

            // Fetch user profile data to get profile image
            try {
              const userResponse = await fetch(`http://localhost:3001/v1/app-users`);
              const userData = await userResponse.json();
              const currentUser = userData.data?.find(user => user.id === loginUser.id);

              return {
                ...loginUser,
                profile: currentUser?.profile || null, // Add profile image
                email: currentUser?.email || null,
                accessToken: loginUser.token || "test",
              };
            } catch (error) {
              console.log("Error fetching user profile:", error);
              return {
                ...loginUser,
                profile: null,
                accessToken: loginUser.token || "test",
              };
            }
          } else {
            console.log("No user found");
            return null;
          }
        } catch (error) {
          console.log("Error:", error.message);
          return null;
        }
      },
    }),
  ],
  session: {
    jwt: false, // You're using session-based auth, not JWT
  },
  jwt: {
    secret: "test",
    encryption: true,
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log('JWT----->',user);
      if (user) {
        token.id = user.id;
        token.role = user.role.toUpperCase();
        token.name = user.username;
        token.profile = user.profile; // Add profile image to token
        token.email = user.email; // Add email to token
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.id;
      session.user.role = token.role;
      session.user.username = token.name;
      session.user.email = token.email; // Add email to session
      session.user.image = token.profile ? `https://hassana-api.360xpertsolutions.com/v1/${token.profile}` : null; // Add profile image URL
      session.accessToken = token.accessToken;
      console.log('JSESSIOWT----->',session);
      return session;
    },
  },
});