import { UserService } from './user.service';
import { User } from './entities/user.entity';
import { UUID } from 'crypto';
export declare class UserResolver {
    private readonly userService;
    constructor(userService: UserService);
    getAllUsers(page: number, pageSize: number): Promise<any>;
    getNewUsers(days: number): Promise<User[]>;
    getCulturalAmbassadors(): Promise<User[]>;
    loginUser(username: string, password: string): Promise<object>;
    findUserById(id: UUID): Promise<User>;
}
