"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuoteResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const quote_service_1 = require("./quote.service");
const create_quote_input_1 = require("./dto/create-quote.input");
const update_quote_input_1 = require("./dto/update-quote.input");
const quote_schema_1 = require("./schema/quote.schema");
let QuoteResolver = class QuoteResolver {
    constructor(quoteService) {
        this.quoteService = quoteService;
    }
    async createQuote(createQuoteInput) {
        try {
            let data = await this.quoteService.create(createQuoteInput);
            return data;
        }
        catch (error) {
            console.log("error in resolver", error);
            return error;
        }
    }
    findAllQuote() {
        return this.quoteService.findAll();
    }
    findOneQuote(id) {
        return this.quoteService.findOne(id);
    }
    todaysQuote() {
        return this.quoteService.findByVisibility();
    }
    updateQuote(id, updateQuoteInput) {
        return this.quoteService.update(id, updateQuoteInput);
    }
    removeQuote(id) {
        return this.quoteService.remove(id);
    }
};
exports.QuoteResolver = QuoteResolver;
__decorate([
    (0, graphql_1.Mutation)(() => quote_schema_1.QuoteSchema),
    __param(0, (0, graphql_1.Args)('createQuoteInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_quote_input_1.CreateQuoteInput]),
    __metadata("design:returntype", Promise)
], QuoteResolver.prototype, "createQuote", null);
__decorate([
    (0, graphql_1.Query)(() => [quote_schema_1.QuoteSchema]),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], QuoteResolver.prototype, "findAllQuote", null);
__decorate([
    (0, graphql_1.Query)(() => quote_schema_1.QuoteSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], QuoteResolver.prototype, "findOneQuote", null);
__decorate([
    (0, graphql_1.Query)(() => quote_schema_1.QuoteSchema),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], QuoteResolver.prototype, "todaysQuote", null);
__decorate([
    (0, graphql_1.Mutation)(() => quote_schema_1.QuoteSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('updateQuoteInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_quote_input_1.UpdateQuoteInput]),
    __metadata("design:returntype", void 0)
], QuoteResolver.prototype, "updateQuote", null);
__decorate([
    (0, graphql_1.Mutation)(() => quote_schema_1.QuoteSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], QuoteResolver.prototype, "removeQuote", null);
exports.QuoteResolver = QuoteResolver = __decorate([
    (0, graphql_1.Resolver)(() => quote_schema_1.QuoteSchema),
    __metadata("design:paramtypes", [quote_service_1.QuoteService])
], QuoteResolver);
//# sourceMappingURL=quote.resolver.js.map