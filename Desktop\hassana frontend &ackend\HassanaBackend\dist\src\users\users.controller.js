"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("./user.service");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const update_user_input_1 = require("./dto/update-user.input");
const swagger_1 = require("@nestjs/swagger");
let multerOptions = {
    storage: (0, multer_1.diskStorage)({
        destination: './resource/v1/users',
        filename: (req, profile, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = (0, path_1.extname)(profile.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        }
    })
};
let UsersController = class UsersController {
    constructor(userService) {
        this.userService = userService;
    }
    async getAllUsers(page = 1, pageSize = 10) {
        try {
            const { users, meta } = await this.userService.findAllUsers(page ?? 1, pageSize ?? 10);
            return {
                code: 200,
                message: "Success",
                data: users,
                meta
            };
        }
        catch (error) {
            console.log(error);
            return {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
        }
    }
    async updateUser(id, updateUserInput, file) {
        try {
            console.log("file: " + file);
            if (file) {
                let path = file?.path;
                var profile = path?.replace(/resource\/v1[\/\\]/g, "");
                ;
            }
            let data = await this.userService.update(id, { ...updateUserInput, profile });
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getAllUsers", null);
__decorate([
    (0, swagger_1.ApiBody)({ type: update_user_input_1.UpdateUserInput }),
    (0, common_1.Patch)('/:id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('profile', multerOptions)),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_input_1.UpdateUserInput, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateUser", null);
exports.UsersController = UsersController = __decorate([
    (0, common_1.Controller)('v1/app-users'),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UsersController);
//# sourceMappingURL=users.controller.js.map