import { ResourceService } from './resource.service';
import { CreateResourceInput } from './dto/create-resource.input';
import { UpdateResourceInput } from './dto/update-resource.input';
import { UUID } from 'crypto';
export declare class ResourceResolver {
    private readonly resourceService;
    constructor(resourceService: ResourceService);
    createResource(createResourceInput: CreateResourceInput): Promise<import("./entities/resource.entity").Resource>;
    findAll(): Promise<import("./entities/resource.entity").Resource[]>;
    findOne(id: UUID): Promise<import("./entities/resource.entity").Resource>;
    updateResource(updateResourceInput: UpdateResourceInput): Promise<import("./entities/resource.entity").Resource>;
    removeResource(id: UUID): Promise<import("./entities/resource.entity").Resource>;
}
