import { UserService } from './user.service';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';
import { UUID } from 'crypto';
export declare class UsersController {
    private readonly userService;
    constructor(userService: UserService);
    getAllUsers(page?: number, pageSize?: number): Promise<{
        code: number;
        message: string;
        data: any[];
        meta: object;
        error?: undefined;
    } | {
        code: any;
        message: any;
        error: any;
        data?: undefined;
        meta?: undefined;
    }>;
    updateUser(id: UUID, updateUserInput: UpdateUserInput, file: Express.Multer.File): Promise<{
        code: number;
        message: string;
        data: User;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
}
