"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Announcement = void 0;
const graphql_1 = require("@nestjs/graphql");
let Announcement = class Announcement {
};
exports.Announcement = Announcement;
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.Int),
    __metadata("design:type", String)
], Announcement.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Announcement.prototype, "title", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Announcement.prototype, "details", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Announcement.prototype, "category", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Announcement.prototype, "status", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.GraphQLISODateTime),
    __metadata("design:type", Date)
], Announcement.prototype, "visibility", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Announcement.prototype, "image", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Announcement.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Announcement.prototype, "updatedAt", void 0);
exports.Announcement = Announcement = __decorate([
    (0, graphql_1.ObjectType)()
], Announcement);
//# sourceMappingURL=announcement.schema.js.map