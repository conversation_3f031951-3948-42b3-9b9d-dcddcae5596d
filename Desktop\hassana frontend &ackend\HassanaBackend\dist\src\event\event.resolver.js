"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const event_service_1 = require("./event.service");
const create_event_input_1 = require("./dto/create-event.input");
const update_event_input_1 = require("./dto/update-event.input");
const event_schema_1 = require("./schema/event.schema");
let EventResolver = class EventResolver {
    constructor(eventService) {
        this.eventService = eventService;
    }
    createEvent(createEventInput) {
        return this.eventService.create(createEventInput);
    }
    findAll() {
        return this.eventService.findAll();
    }
    findOne(id) {
        return this.eventService.findOne(id);
    }
    TodaysEvents(date, category) {
        return this.eventService.findTodaysEvent(date, category);
    }
    updateEvent(id, updateEventInput) {
        return this.eventService.update(id, updateEventInput);
    }
    removeEvent(id) {
        return this.eventService.remove(id);
    }
};
exports.EventResolver = EventResolver;
__decorate([
    (0, graphql_1.Mutation)(() => event_schema_1.Event),
    __param(0, (0, graphql_1.Args)('createEventInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_event_input_1.CreateEventInput]),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "createEvent", null);
__decorate([
    (0, graphql_1.Query)(() => [event_schema_1.Event], { name: 'events' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "findAll", null);
__decorate([
    (0, graphql_1.Query)(() => event_schema_1.Event, { name: 'event' }),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "findOne", null);
__decorate([
    (0, graphql_1.Query)(() => [event_schema_1.Event], { name: 'todaysEvents' }),
    __param(0, (0, graphql_1.Args)('date', { type: () => Date })),
    __param(1, (0, graphql_1.Args)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date, String]),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "TodaysEvents", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_schema_1.Event),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('updateEventInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_event_input_1.UpdateEventInput]),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "updateEvent", null);
__decorate([
    (0, graphql_1.Mutation)(() => event_schema_1.Event),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], EventResolver.prototype, "removeEvent", null);
exports.EventResolver = EventResolver = __decorate([
    (0, graphql_1.Resolver)(() => event_schema_1.Event),
    __metadata("design:paramtypes", [event_service_1.EventService])
], EventResolver);
//# sourceMappingURL=event.resolver.js.map