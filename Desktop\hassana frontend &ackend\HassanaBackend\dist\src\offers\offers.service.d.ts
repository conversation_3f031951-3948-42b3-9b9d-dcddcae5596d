import { CreateOffersInput } from './dto/create-offer.dto';
import { UpdateOffersInput } from './dto/update-offer.dto';
import { Offers } from './entities/offers.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
import { OffersViewEntity } from './entities/offers.entity';
export declare class OffersService {
    private readonly offersRepository;
    private readonly offersViewRepository;
    constructor(offersRepository: Repository<Offers>, offersViewRepository: Repository<OffersViewEntity>);
    create(createOffersInput: CreateOffersInput): Promise<Offers>;
    findAll(userId: UUID, filter: Object): Promise<Offers[]>;
    findOne(filter: Object): Promise<Offers>;
    createOfferView(offerId: UUID, userId: UUID): Promise<import("typeorm").InsertResult>;
    update(id: UUID, updateOffersInput: UpdateOffersInput): Promise<Offers>;
    remove(id: UUID): Promise<Offers | null>;
}
