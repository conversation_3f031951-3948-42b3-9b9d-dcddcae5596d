"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LibraryController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const library_service_1 = require("./library.service");
let multerOptions = {
    storage: (0, multer_1.diskStorage)({
        destination(req, file, callback) {
            const fileName = file.originalname;
            if (!file)
                return callback(Error("Please upload any file"), fileName);
            if (file?.mimetype != "application/pdf")
                return callback(new Error("Only pdf files are allowed"), fileName);
            callback(null, "./library/v1/lib");
        },
        filename: (req, file, callback) => {
            const fileName = file.originalname;
            callback(null, fileName);
        },
    }),
};
let LibraryController = class LibraryController {
    constructor(libraryService) {
        this.libraryService = libraryService;
    }
    async createFile(file) {
        try {
            let file_path = file?.path?.replace(/library\/v1[\/\\]/g, '');
            console.log(file_path);
            let data = await this.libraryService.create({
                file_name: file.originalname,
                file_path: file_path,
                file_type: file.mimetype
            });
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            console.log("error : ", error);
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
        ;
    }
    ;
    async findAllLibrary() {
        try {
            let data = await this.libraryService.findAll();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error,
            };
            return Response;
        }
    }
    ;
    async removeLibrary(id) {
        try {
            let data = await this.libraryService.remove(id);
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error,
            };
            return Response;
        }
        ;
    }
    ;
};
exports.LibraryController = LibraryController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', multerOptions)),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LibraryController.prototype, "createFile", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LibraryController.prototype, "findAllLibrary", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LibraryController.prototype, "removeLibrary", null);
exports.LibraryController = LibraryController = __decorate([
    (0, common_1.Controller)('v1/library'),
    __metadata("design:paramtypes", [library_service_1.LibraryService])
], LibraryController);
;
//# sourceMappingURL=library.controller.js.map