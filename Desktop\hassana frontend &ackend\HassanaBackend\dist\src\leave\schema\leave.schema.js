"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Leave = void 0;
const graphql_1 = require("@nestjs/graphql");
let Leave = class Leave {
};
exports.Leave = Leave;
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.Int),
    __metadata("design:type", String)
], Leave.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.Int),
    __metadata("design:type", String)
], Leave.prototype, "userid", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Leave.prototype, "username", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Leave.prototype, "remarks", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Leave.prototype, "date", void 0);
__decorate([
    (0, graphql_1.Field)((type) => graphql_1.Int),
    __metadata("design:type", Number)
], Leave.prototype, "numberOfDays", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Leave.prototype, "typeOfLeave", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Leave.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], Leave.prototype, "updatedAt", void 0);
exports.Leave = Leave = __decorate([
    (0, graphql_1.ObjectType)()
], Leave);
//# sourceMappingURL=leave.schema.js.map