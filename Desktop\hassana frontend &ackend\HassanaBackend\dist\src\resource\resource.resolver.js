"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const resource_service_1 = require("./resource.service");
const create_resource_input_1 = require("./dto/create-resource.input");
const update_resource_input_1 = require("./dto/update-resource.input");
const resource_schema_1 = require("./schema/resource.schema");
let ResourceResolver = class ResourceResolver {
    constructor(resourceService) {
        this.resourceService = resourceService;
    }
    async createResource(createResourceInput) {
        return await this.resourceService.create(createResourceInput);
    }
    async findAll() {
        return await this.resourceService.findAll();
    }
    async findOne(id) {
        return await this.resourceService.findOne(id);
    }
    updateResource(updateResourceInput) {
        return this.resourceService.update(updateResourceInput.id, updateResourceInput);
    }
    removeResource(id) {
        return this.resourceService.remove(id);
    }
};
exports.ResourceResolver = ResourceResolver;
__decorate([
    (0, graphql_1.Mutation)(() => resource_schema_1.ResourceSchema),
    __param(0, (0, graphql_1.Args)('createResourceInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_resource_input_1.CreateResourceInput]),
    __metadata("design:returntype", Promise)
], ResourceResolver.prototype, "createResource", null);
__decorate([
    (0, graphql_1.Query)(() => [resource_schema_1.ResourceSchema], { name: 'allResources' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResourceResolver.prototype, "findAll", null);
__decorate([
    (0, graphql_1.Query)(() => resource_schema_1.ResourceSchema, { name: 'singleResource' }),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ResourceResolver.prototype, "findOne", null);
__decorate([
    (0, graphql_1.Mutation)(() => resource_schema_1.ResourceSchema),
    __param(0, (0, graphql_1.Args)('updateResourceInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_resource_input_1.UpdateResourceInput]),
    __metadata("design:returntype", void 0)
], ResourceResolver.prototype, "updateResource", null);
__decorate([
    (0, graphql_1.Mutation)(() => resource_schema_1.ResourceSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ResourceResolver.prototype, "removeResource", null);
exports.ResourceResolver = ResourceResolver = __decorate([
    (0, graphql_1.Resolver)(() => resource_schema_1.ResourceSchema),
    __metadata("design:paramtypes", [resource_service_1.ResourceService])
], ResourceResolver);
//# sourceMappingURL=resource.resolver.js.map