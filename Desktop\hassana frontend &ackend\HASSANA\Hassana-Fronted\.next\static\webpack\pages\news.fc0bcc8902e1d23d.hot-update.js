"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/news",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject2());\n// Styled components for social media style notifications\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    onClick: ()=>removeHandler(notificationData.id),\n                                                    sx: {\n                                                        p: 2,\n                                                        borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                        \"&:hover\": {\n                                                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                            cursor: \"pointer\"\n                                                        },\n                                                        transition: \"all 0.2s ease-in-out\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                gap: 2,\n                                                                alignItems: \"flex-start\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        borderRadius: \"50%\",\n                                                                        //background: th,\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        justifyContent: \"center\",\n                                                                        flexShrink: 0,\n                                                                        boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                                        sx: {\n                                                                            color: \"white\",\n                                                                            fontSize: \"20px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    sx: {\n                                                                        flex: 1,\n                                                                        minWidth: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"body1\",\n                                                                            sx: {\n                                                                                fontSize: \"14px\",\n                                                                                fontWeight: 500,\n                                                                                lineHeight: \"20px\",\n                                                                                color: \"#333\",\n                                                                                mb: 0.5,\n                                                                                wordBreak: \"break-word\"\n                                                                            },\n                                                                            children: notificationData.notification\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            sx: {\n                                                                                fontSize: \"12px\",\n                                                                                color: \"#666\",\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\",\n                                                                                gap: 0.5\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    sx: {\n                                                                                        fontSize: \"4px\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            sx: {\n                                                                position: \"absolute\",\n                                                                left: 0,\n                                                                top: 0,\n                                                                bottom: 0,\n                                                                width: \"3px\",\n                                                                //background:\n                                                                //  \"linear-gradient(180deg, #667eea 0%, #764ba2 100%)\",\n                                                                borderRadius: \"0 2px 2px 0\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, notificationData.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            gap: 1,\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 457,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showBadgeCount, setShowBadgeCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    // Derive userId from session\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    // Handle session loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 479,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 478,\n            columnNumber: 7\n        }, this);\n    }\n    // Query for new notifications\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNewNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    // Notification sound function\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    // Show browser notification\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    // Request notification permission\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    // Debug session object\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    // Handle notification query results\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.newNotificationsForUser);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.newNotificationsForUser)) {\n            const newNotifications = data.newNotificationsForUser;\n            const currentCount = newNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"Notifications received:\", newNotifications);\n            console.log(\"Count:\", currentCount);\n            console.log(\"showBadgeCount state:\", showBadgeCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 3000);\n            }\n            setNotifications(newNotifications);\n            setPreviousNotificationCount(currentCount);\n            // Only show badge count if there are notifications and the panel is not currently open\n            if (currentCount > 0 && !notificationOpen) {\n                setShowBadgeCount(true);\n                console.log(\"Setting showBadgeCount to TRUE because count > 0 and panel is closed\");\n            } else if (currentCount === 0) {\n                setShowBadgeCount(false);\n                console.log(\"Setting showBadgeCount to FALSE because no notifications\");\n            }\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            // Retry polling after 30 seconds\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = ()=>{\n        setNotificationOpen((prev)=>!prev);\n    // Don't automatically mark notifications as viewed when opening the panel\n    // Users need to click on individual notifications to mark them as seen\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n    // Don't hide badge count when closing - it should always show unseen notification count\n    };\n    const markAllNotificationsAsViewed = async ()=>{\n        if (!userId || notifications.length === 0) return;\n        try {\n            const promises = notifications.map((notification)=>addNotificationView({\n                    variables: {\n                        notificationId: notification.id,\n                        user_id: userId\n                    }\n                }));\n            await Promise.all(promises);\n            // Clear notifications and reset counts\n            setNotifications([]);\n            setPreviousNotificationCount(0);\n            setShowBadgeCount(false);\n            console.log(\"All notifications marked as viewed and cleared\");\n        } catch (error) {\n            console.error(\"Error marking all notifications as viewed:\", error);\n        }\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                // Remove the notification from the list immediately\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                // Update badge count to show only unseen notifications\n                setShowBadgeCount(true);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 740,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 747,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 733,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 758,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 757,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 750,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: '\"\"',\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 798,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"blue\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"blue\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"green\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"green\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        sx: {\n                                                                            color: \"purple\"\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    sx: {\n                                                                        color: \"purple\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 862,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 826,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 816,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"6px\",\n                                                    sm: \"8px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: notifications.length,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8pxrgb(122, 15, 15))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 895,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 879,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 878,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 877,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 917,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 767,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 720,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 719,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid #EBEBEB\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 956,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 947,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 969,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 996,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1010,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1003,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1016,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1015,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1020,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1014,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1026,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1025,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1030,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1040,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1046,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1045,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1050,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1043,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 968,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1059,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1058,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 958,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 939,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 931,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1069,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana frontend &ackend\\\\HASSANA\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1064,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Ra7y/5uHrJet5+UvmlxfSvo3ne4=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});