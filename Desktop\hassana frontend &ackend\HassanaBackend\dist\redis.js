"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redis = void 0;
const ioredis_1 = require("ioredis");
const dotenv = require("dotenv");
dotenv.config();
exports.redis = new ioredis_1.default({
    port: parseInt(process.env.REDIS_PORT),
    host: process.env.REDIS_HOST,
    password: 'foobared',
});
exports.redis.on('error', (error) => {
    console.error('Redis error:', error);
});
module.exports = { redis: exports.redis };
//# sourceMappingURL=redis.js.map