import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNotificationInput } from './dto/create-notification.input';
import { UpdateNotificationInput } from './dto/update-notification.input';
import { Repository } from 'typeorm';
import { NotificationEntity, NotificationViewEntity } from './entities/notification.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';

@Injectable()
export class NotificationService {

  constructor(
    @InjectRepository(NotificationEntity)
    private readonly notificationRepo: Repository<NotificationEntity>,
    @InjectRepository(NotificationViewEntity)
    private readonly notificationViewRepo: Repository<NotificationViewEntity>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>
  ) { }

  async create(createNotificationInput: CreateNotificationInput) {
    try {
      const response = await this.notificationRepo.save(createNotificationInput);
      return response;

    } catch (error) {
      console.log(error);
      throw Error(error)

    }
  }

  async addView(notificationId: UUID, userId: UUID) {

    try {
      // Check if the view already exists
      const existingView = await this.notificationViewRepo.findOne({
        where: {
          user: { id: userId },
          notification: { id: notificationId },
        },
      });

      if (existingView) {
        console.log('User has already viewed this notification.');
        return existingView;
      }

      const newView = new NotificationViewEntity();
      newView.user = { id: userId } as User; // Cast it to User for TypeORM to understand
      newView.notification = { id: notificationId } as NotificationEntity; // Cast it to NotificationEntity

      // Save the new view
      const savedView = await this.notificationViewRepo.save(newView);
      console.log('New view added:', savedView);
      return savedView;
    } catch (error) {
      console.error('Error in setUserViewedNotification:', error.message);
      throw new Error(error);
    }
  }

  async findAllNotificationViews() {
    try {
      let response = await this.notificationViewRepo.find();
      console.log(response);
      return response;

    } catch (error) {
      console.log(error.message);
      throw Error(error)
    }
  }
  async findAll() {
    try {
      let response = await this.notificationRepo.find({
        order: {
          createdAt: 'DESC' // Latest notifications first
        }
      });
      return response;

    } catch (error) {
      console.log(error.message);
      throw Error(error)
    }
  }
  async getAllNewNotification() {
    try {
      let response = await this.notificationRepo.find({ relations: ['NotificationViewEntity'] });
      return response;

    } catch (error) {
      console.log(error.message);
      throw Error(error)
    }
  }


  async getAllNewNotificationsForUser(userId: number) {
    try {
      const viewedNotificationsSubquery = this.notificationViewRepo.createQueryBuilder('view')
        .select('view.notificationId')
        .where('view.userId = :userId', { userId });

      const queryBuilder = this.notificationRepo.createQueryBuilder('notification');
      queryBuilder.where(`notification.id NOT IN (${viewedNotificationsSubquery.getQuery()})`)
                  .orderBy('notification.createdAt', 'DESC') // Latest notifications first
                  .setParameters({ userId });  // Set parameters for the subquery

      const response = await queryBuilder.getMany();
      console.log(response);
      return response;
    } catch (error) {
      console.error('Error in getAllNewNotificationsForUser:', error.message);
      throw new Error(error);
    }
  }

  async getUnseenNotificationsCount(userId: UUID): Promise<number> {
    try {
      const viewedNotificationsSubquery = this.notificationViewRepo.createQueryBuilder('view')
        .select('view.notificationId')
        .where('view.userId = :userId', { userId });

      const queryBuilder = this.notificationRepo.createQueryBuilder('notification');
      queryBuilder.where(`notification.id NOT IN (${viewedNotificationsSubquery.getQuery()})`)
                  .setParameters({ userId });

      const count = await queryBuilder.getCount();
      console.log(`Unseen notifications count for user ${userId}: ${count}`);
      return count;
    } catch (error) {
      console.error('Error in getUnseenNotificationsCount:', error.message);
      throw new Error(error);
    }
  }

  async markAllNotificationsAsSeen(userId: UUID): Promise<boolean> {
    try {
      // Get all notifications that are not yet viewed by this user
      const unseenNotifications = await this.getAllNewNotificationsForUser(userId as any);

      // Create notification views for all unseen notifications
      const notificationViews = unseenNotifications.map(notification => {
        const view = new NotificationViewEntity();
        view.user = { id: userId } as User;
        view.notification = { id: notification.id } as NotificationEntity;
        return view;
      });

      if (notificationViews.length > 0) {
        await this.notificationViewRepo.save(notificationViews);
        console.log(`Marked ${notificationViews.length} notifications as seen for user ${userId}`);
      }

      return true;
    } catch (error) {
      console.error('Error in markAllNotificationsAsSeen:', error.message);
      throw new Error(error);
    }
  }

  findOne(id: UUID) {
    return `This action returns a #${id} notification`;
  }

  async update(id: UUID, updateNotificationInput: UpdateNotificationInput) {
    try {
      let oldData = await this.notificationRepo.findOne({ where: { id: id } });
      console.log(oldData);

      if (!oldData) throw new NotFoundException("Quote not found")

      if (oldData) {
        this.notificationRepo.merge(oldData, updateNotificationInput);
        let newData = await this.notificationRepo.save(updateNotificationInput);
        return newData;
      }

    } catch (error) {
      throw new Error(error.message)
    }
  }

  async remove(id: UUID) {
    try {
      let data = await this.notificationRepo.findOne({ where: { id: id } });

      if (!data) throw new NotFoundException("Notification not found")

      if (data) {
        return await this.notificationRepo.remove(data);
      }

    } catch (error) {
      throw new Error(error.message)

    }
  }
}
