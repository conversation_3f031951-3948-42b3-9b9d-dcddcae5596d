"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const apollo_1 = require("@nestjs/apollo");
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const config_1 = require("@nestjs/config");
const platform_express_1 = require("@nestjs/platform-express");
const typeorm_1 = require("@nestjs/typeorm");
const path_1 = require("path");
const announcement_module_1 = require("./announcement/announcement.module");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const booking_module_1 = require("./booking/booking.module");
const event_module_1 = require("./event/event.module");
const resource_module_1 = require("./resource/resource.module");
const test_module_1 = require("./test/test.module");
const user_module_1 = require("./users/user.module");
const quote_module_1 = require("./quote/quote.module");
const notification_module_1 = require("./notification/notification.module");
const news_module_1 = require("./news/news.module");
const exchange_info_module_1 = require("./exchange-info/exchange-info.module");
const leave_module_1 = require("./leave/leave.module");
const file_upload_module_1 = require("./file-upload/file-upload.module");
const library_module_1 = require("./library/library.module");
const offers_module_1 = require("./offers/offers.module");
const user_feedback_module_1 = require("./user-feedback/user-feedback.module");
const user_reviews_module_1 = require("./user-reviews/user-reviews.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: `.env`,
            }),
            graphql_1.GraphQLModule.forRoot({
                driver: apollo_1.ApolloDriver,
                playground: true,
                introspection: true,
                autoSchemaFile: (0, path_1.join)(process.cwd(), 'src/auto-generated-schema/schema.graphql'),
                definitions: {
                    path: (0, path_1.join)(process.cwd(), 'src/auto-generated-schema/graphql.ts'),
                },
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                host: process.env.DB_HOST,
                port: parseInt(process.env.DB_PORT),
                username: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME,
                useUTC: true,
                entities: [__dirname + '/**/*.entity{.ts,.js}'],
                synchronize: true,
                logging: true
            }),
            platform_express_1.MulterModule.register(),
            test_module_1.TestModule,
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            news_module_1.NewsModule,
            booking_module_1.BookingModule,
            resource_module_1.ResourceModule,
            announcement_module_1.AnnouncementModule,
            event_module_1.EventModule,
            quote_module_1.QuoteModule,
            notification_module_1.NotificationModule,
            exchange_info_module_1.ExchangeInfoModule,
            leave_module_1.LeaveModule,
            file_upload_module_1.FileUploadModule,
            library_module_1.LibraryModule,
            offers_module_1.OfferModule,
            user_feedback_module_1.UserFeedbackModule,
            user_reviews_module_1.UserReviewsModule
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map