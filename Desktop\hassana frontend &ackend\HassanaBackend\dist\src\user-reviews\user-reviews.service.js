"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserReviewsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_review_entity_1 = require("./entities/user-review.entity");
const typeorm_2 = require("typeorm");
let UserReviewsService = class UserReviewsService {
    constructor(reviewRepository) {
        this.reviewRepository = reviewRepository;
    }
    async create(createUserReviewDto, user_id) {
        if (createUserReviewDto.user_id === user_id)
            throw Error("user can't give review to his own profile");
        const now = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setUTCDate(thirtyDaysAgo.getUTCDate() - 30);
        let existingReviews = await this.findAll({
            created_by: user_id,
            createdAt: (0, typeorm_2.Between)(thirtyDaysAgo, now)
        });
        createUserReviewDto['created_by'] = user_id;
        if (existingReviews.length >= 3)
            throw Error("you are not allowed to review more than a three times in a month");
        const create = this.reviewRepository.create(createUserReviewDto);
        return await this.reviewRepository.save(create);
    }
    async findAll(filter) {
        const data = await this.reviewRepository.find({ where: filter });
        return data;
    }
    findOne(id) {
        return `This action returns a #${id} userReview`;
    }
    update(id, updateUserReviewDto) {
        return `This action updates a #${id} userReview`;
    }
    remove(id) {
        return `This action removes a #${id} userReview`;
    }
};
exports.UserReviewsService = UserReviewsService;
exports.UserReviewsService = UserReviewsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_review_entity_1.UserReview)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserReviewsService);
//# sourceMappingURL=user-reviews.service.js.map