import { CreateLibraryInput } from './dto/create-library.input';
import { Library } from './entities/library.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class LibraryService {
    private readonly libraryRepository;
    constructor(libraryRepository: Repository<Library>);
    create(createLibraryInput: CreateLibraryInput): Promise<Library>;
    findAll(): Promise<Library[]>;
    remove(id: UUID): Promise<Library>;
}
