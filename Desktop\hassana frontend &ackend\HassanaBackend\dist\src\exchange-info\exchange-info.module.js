"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExchangeInfoModule = void 0;
const common_1 = require("@nestjs/common");
const exchange_info_service_1 = require("./exchange-info.service");
const exchange_info_controller_1 = require("./exchange-info.controller");
let ExchangeInfoModule = class ExchangeInfoModule {
};
exports.ExchangeInfoModule = ExchangeInfoModule;
exports.ExchangeInfoModule = ExchangeInfoModule = __decorate([
    (0, common_1.Module)({
        controllers: [exchange_info_controller_1.ExchangeInfoController],
        providers: [exchange_info_service_1.ExchangeInfoService],
    })
], ExchangeInfoModule);
//# sourceMappingURL=exchange-info.module.js.map